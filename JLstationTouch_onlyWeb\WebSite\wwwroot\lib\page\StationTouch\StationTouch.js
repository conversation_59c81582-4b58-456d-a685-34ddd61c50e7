hc.init({
    UId: "StationTouch",
    components: [
        "hc-row",
        "hc-col",
        "hc-button",
        "hc-input",
        "hc-select",
        "hc-option",
        "hc-table",
        "hc-form",
        "hc-form-item",
        "hc-tree",
        'hc-tree-select',
        'hc-field-layout',
        'hc-field-layout-full',
        'hc-checkbox',
        'hc-tabs',
        'hc-tab-pane',
        'hc-bar-box',
        "hc-label-value",
        "hc-value",
        "hc-title-content",
        "hc-pcd",
        "hc-select",
        "hc-date-picker",
        "hc-open-search",
        "hc-upload",
        "hc-qrcode-dialog",
        "hc-image",
        "hc-dialog",
        "hc-cascader",
        "hc-el-table",
        "hc-el-table-column",
        "hc-qrcode-dialog",
        "hc-common-modal",
        "hc-print"
    ],
    plugins: ["lodash", "moment", 'jsencrypt'],
    data() {
        let inputRequire = hc.ViewModel.$t('请输入(必填)');
        let dropDownRequire = hc.ViewModel.$t('请选择(必选)');
        let inputAllowNull = hc.ViewModel.$t('请输入');
        let dropDownAllowNull = hc.ViewModel.$t('请选择');
        return {
            company: "JL",
            ip: '',//本机本地内网ip
            backColor: '',//影响背景颜色变量  newJob  errorJob

            //加工状态字典数据
            platDicDatas_FCRAFT_STATUS: {},


            //级联选择工位属性
            stationTree: [],

            //---登录用户信息，切换用户使用---
            PublicKey: '',

            SwitchUserFormMst: {
                FUSERNAME: '',
                FPASSWORD: '',
                IsNeedCode: false,
                CodeKey: "",
                FLANG: "CN",
            },
            SwitchUserDialogVisible: false,
            SwitchUserListVisible: false,
            btnshow: false,
            AllEmpData: [],
            selectEmpData: null,//table内选中的用户信息

            rules: {
                FUSERNAME: [
                    { required: true, message: hc.ViewModel.$t('请输入手机号'), trigger: "blur" },
                    { validator: this.ValidateMobile, trigger: "blur" },
                ],
                FPASSWORD: [
                    { required: true, message: hc.ViewModel.$t('请输入密码'), trigger: "blur" },
                    { validator: this.ValidatePassword, trigger: "blur" },
                ],
            },
            FDEPT_ID: '',
            FDEPT_ID_props: {
                dataType: "ADM024Maker",
                source: [],
                placeholder: '请选择部门',
                valueKey: "Id",
                labelKey: "Label",
            },
            //---------end-----------

            api: {
                // getCheckPaneData: '/api/ems001equdata/getlistasync',//获取检查数据页签方法

                getEntDicByCodesType: '/api/EntDataDic/QueryByCodesAsync', //企业数据字典
                GetFinishTagByID: "/api/MES007JobBooking/GetFinishTagByID",//获取标签明细


                initPage: '/api/ClientUser/GetPublicLoginKeyAsync',//获取 PublicKey
                login: '/api/ClientUser/LoginAsync',// 登录接口
                login: '/api/ClientUser/LoginAsync',// 登录接口

                getPlatDicByCodesType: '/api/datadic/QueryByCodesAsync', //内定数据字典
                GetSkApi: '/api/jialeapiuniapi/getshukonginfo', //获取数据采集设备
                getStationTree: '/api/MES001Station/GetStationTreeAsync',//获取工位树数据

                // getWaitJobs: "/api/MES007JobBooking/QueryWaitJobsJINJIAsync",//获取默认等待任务列表  金技专用
                getWaitJobs: "/api/MES007JobBooking/QueryWaitJobsChuangPaiAsync",//获取默认等待任务列表  创牌专用

                GetMaterialIdAllAsync: "/api/MES007JobQc/GetMaterialIdAllAsync",//获取巡检单信息页
                getEsopList: "/api/AttachmentUp/GetAttachmentListAsync",//获取工艺档案附件信息（产品信息，ESOP页签）
                getCraftScheduleJob: "/api/MES007JobBooking/QueryCraftScheduleJobAsync",//根据编号获取排程任务 排程编号扫描时、输入时
                CreatePackBarCodeAsync: "/api/MES007JobBooking/CreatePackBarCodeAsync",//包装条码

                getDevList: "/api/ems001book/GetListAsync",//获取设备下拉源列表
                GetBadByIdAsync: "/api/MES007JobQc/GetStationBadListsync", //获取不良数据
                getPrdData: "/api/MES003WorkOrder/GetByIdAsync",//获取产品信息表 获取生产工单的物料子表
                getMoldData: '/api/ems002moldrecord/getbyworkordidasync',//根据工单id获取模具信息
                getIuiuFormula: '/api/iu010_universalapi/GetByWoIdAsync',//获取悠悠美居配方接口

                getDevData: '/api/ems001book/GetByIdAsync', // 获取设备数据详情
                getCheckPaneData: '/api/ems001equdata/getlistasync',//获取设备信息方法
                getStationData: '/api/MES001Station/GetByIdAsync',//获取工位信息方法
                getMaterialData: '/api/MSD002Material/GetMaterialByIdAsync',//获取物料产品数据 主要是图片


                StartJobAsync: "/api/MES007JobBooking/BatchStartJobAsync",//开工
                CancelJobAsync: "/api/MES007JobBooking/CancelJobAsync",//取消开工
                FinishJobAsync: "/api/MES007JobBooking/BatchFinishJobAsync",//完工
                BatchSplitAsync: "/api/MES007JobBooking/BatchFinishJobWithSplitAsync",//批量拆分完工
                PauseJobAsync: "/api/MES007JobBooking/BatchPauseJobAsync",//暂停
                ResumeJobAsync: "/api/MES007JobBooking/BatchResumeJobAsync",//恢复
                endClose: "/api/ChuangPaiUniApi/CloseCraftScheduleAsync",//结案

                GetStationByIp: '/api/MES001Station/GetStationByIpAsync',//根据当前IP地址 获取工位机信息，工位id，设备id

                udgeNewScheduleByStationId: '/api/MES005CraftSchedule/JudgeNewScheduleByStationIdAsync',//查询当前工位是否有新的排程任务

                GetAttachmentInfoAsync: '/api/AttachmentUp/GetAttachmentInfoAsync',//根据id 获取 pdf真实地址 接口

                reimburselable: '/api/iu010_universalapi/reimburselableasync',//报工明细补打标签接口
                canceljobbooking: '/api/iu010_universalapi/canceljobbookingasync',//作废报工明细行接口
                getheadImg: 'api/Employee/GetEmpByIdAsync  ',//获取组织员工头像

                //----报工明细表---
                getList: "/api/MES007JobQuery/JobBookingDetailAsync",
                cancelFinished: "/api/MES007JobBooking/CancelCompletion",
                getJobList: "/api/MES007JobBooking/QueryJobBookingAsync",
                saveJob: "/api/MES007JobBooking/UpdateJobBookingAsync",
                //----end-----


                //获取登录员工api
                GetAllEmp: "/api/Employee/GetAllEmpAsync",

                //修改为 获取 产品工艺路线sop 里的附件 
                GetAttachmentListAsync: '/api/AttachmentUp/GetAttachmentListAsync',//获取sop附件

                GetBadReasonCate: "/api/QCS001BadReasonCate/GetForBusinessAsync",//获取不良原因分类
                loadBadReasonList: "/api/QCS001BadReason/GetAllAsync",//获得不良原因数据源

                //不合格数量、不合格原因单独保存接口
                chuangPaiSaveNg: "/api/ChuangPaiUniApi/SaveAsync", // 弃用 改为引用MES007_JobBooking.SaveJobBookingQcAsync
                QcNgSave: "/api/MES007JobQc/SaveJobBookingQcAsync",


                GetCraftSchByIds: "/api/ChuangPaiUniApi/GetCraftSchByIdsAsync",//获取额外的排程任务信息  金技专用


                // 根据工单id获取所有工艺的不良总数
                GetWorkOrdJobBookingBad: "/api/ChuangPaiUniApi/GetWorkOrdJobBookingBadAsync",


                getCounterByStation: '/api/ZhaokeApiUniApi/GetCounterByStationAsync', //获取工位机完成数量



            },
            urlParam: {
                editId: hc.Command.GetUrlParam("editId") || "",
                status: hc.Command.GetUrlParam("action") || "add",
                cateId: hc.Command.GetUrlParam("cateId") || "",
            },
            params: {
                PageSize: 30,
                PageIndex: 1,
                WhereGroup: {
                    Groups: [],
                    Items: [],
                    GroupType: "AND"
                }
            },

            //---------end--------

            userName: '',//用户名字
            User: '',//登录用户id
            // Equipment: '9632bb1089ba414a8385f286d0f74f6a',//默认先给压延设备
            //主from
            mst: {
                // User: '',
                FSTATION_ID: '',//默认先给一个工位id  吹膜工位的id
                FCRAFT_SCHEDULE_ID: '',//默认先给一个排程工艺的id  018639d5eab54fd7b52d69215a2a3155
                FCRAFT_SCHEDULE_NO: '',//默认先给一个排程编号  202112070001

                // FMATERIAL_BOM_ID: '8b8a51a20ad3444793bfec1a35a1bcac',//默认先给一个BOM  id
                FWORK_ORDER_ID: '',//默认先给一个工单的  id  1b0e2bf8c7354ed4823b747a2f949d22
                FWORK_ORDER_NO: '',//排程任务中的工单编号


                FMATERIAL_NAME: '产品名称',
                FCRAFT_NAME: '工艺名称',
                FCRAFT_CODE: '工艺编号',

                FBOOK_ID: '',//设备id



            },

            FJUAN_STATUS: 0,
            cancel_FJUAN_STATUS: false,
            FBOOK_CODE: '',//设备代号
            FGOODS_MODEL: '',//存货描述


            //---排程任务状态对象信息---
            HandlingJobs: {

            },

            UpdateJobData_FPASS_QTY_bak: 0,
            FWORK_STATUS: 'original',//排程任务状态  初始值 original  进行中 working  暂停中 paused
            FACT_ST_DATE: '',//开始时间
            FACT_ED_DATE: '',//完工时间
            FACT_USE_HOUR: 0,//任务所用时长
            WorkingHours: '',//任务时长

            refreshDevData: [],//设备刷新数据
            recvdatetime: '',//数据更新日期时间

            refreshCheckNewJobTimer: 0,// 根据工位id查询是否有新任务定时器

            centerDialogVisible: false,//完工弹窗显示否
            centerDialogItemVisible: false,//是否显示子表
            ngDialogVisible: false,//不合格数量弹窗
            dialogTitle: "部分完工报工",//弹窗标题
            operationType: "finish",//操作类型：finish-部分完工，split-批量拆分

            pdfDialogVisible: false,
            //预览PDF对话框高度 默认1000px
            objectHeight: '1000px',

            // http://mes:5000/MSD002Attach/4b026d60ac4c4df7bbd09e8eef7b8038.pdf
            pdfSrc: '',//   /static/youyou.pdf

            msgAudio: '/static/message.mp3',

            FPASS_QTY: 0, //合格数量
            FPASS_WEIGHT: 0, //合格重量
            FNG_QTY: 0, //不良数量
            FNG_WEIGHT: 0, //不良重量

            UpdateJobDialogVisible: false,//修改框是否出现
            UpdateColorDialogVisible: false,//修改颜色
            //修改的行数据记录
            UpdateJobData: {
                FPASS_QTY: 0, //合格数量
                FPASS_WEIGHT: 0, //合格重量
                FNG_QTY: 0, //不良数量
                FNG_WEIGHT: 0, //不良重量
            },
            //排程明细 当前行数据是否作废
            // FJUAN_STATUS: 0,
            // cancel_FJUAN_STATUS: false,
            MaterialWeight: 0,//工单物料拓展信息（端子净重KG/KPCS）

            mstForm: {
                props: {
                    layoutGrid: true,
                    rules: {

                    }
                },
                events: {
                    name: ["validate"],
                    handler: hc.ViewModel.formEventsHander
                }
            },
            color: {

                FLOT_NO_COLOR: '',//流程卡颜色

            },

            colorForm: {
                props: {
                    layoutGrid: true,
                    rules: {

                        FLOT_NO_COLOR: [{
                            required: true,
                            message: hc.Command.StringFormat(hc.ViewModel.$t('请选择{0}'), hc.ViewModel.$t('流程卡颜色')),
                            trigger: "blur"
                        }],
                    }
                },
                events: {
                    name: ["validate"],
                    handler: hc.ViewModel.formEventsHander
                }
            },

            // //二维码属性
            // qrcodeDialog: {
            //     props: {
            //         title: "",
            //         value: "",
            //     },
            // },

            // fileList: [], //简图list

            controls: {
                props: {
                    //用户
                    User: {
                        placeholder: '用户',
                        disabled: true,
                        readOnly: true,
                    },

                    // 生产工单编号
                    FWORK_ORDER_NO: {
                        placeholder: '生产工单',
                        maxLength: 50,
                        readonly: false,
                        disabled: true,
                        dataType: "WorkOrderOpen",
                        dataPropName: "FWORK_ORDER_NO",
                        openSearchTitle: hc.ViewModel.$t("生产工单"),
                    },

                    //流程卡颜色
                    FLOT_NO_COLOR: {
                        source: [],
                        filterable: true,
                        //valueKey: "FDIC_ITEM_CODE",
                        valueKey: "FDIC_ITEM_DESC",
                        labelKey: "FDIC_ITEM_DESC",
                        placeholder: '请选择流程卡颜色',
                        disabled: false,
                    },

                    //工位
                    FSTATION_ID: {
                        value: 'ID',
                        label: "NAME",
                        children: "SubCates",
                        emitPath: false,
                    },

                    //设备
                    FBOOK_ID: {
                        placeholder: '设备',
                        source: [],
                        valueKey: "FBOOK_ID",
                        labelKey: "FBOOK_NAME",
                        disabled: true,
                        readOnly: true,
                    },

                    // 排程
                    FCRAFT_SCHEDULE_NO: {
                        placeholder: '请选择排程号',
                        disabled: true,
                        readOnly: true,
                    },

                    //不良原因
                    itemDialog: {
                        showFooter: false,
                        width: "70%",
                        height: "70%",
                        title: hc.ViewModel.$t('不良原因'),
                        visible: false,
                    },
                },
                events: {
                    //工位事件
                    FSTATION_ID: {
                        name: ["change"],
                        handler: hc.ViewModel.FSTATION_ID_EventHandler,
                    },

                    //选择 生产工单
                    FWORK_ORDER_NO: {
                        name: ["afterSelectData", "clearValue"],
                        handler: hc.ViewModel.FWORK_ORDER_NO_EventHandler,
                    },
                }
            },
            FJieTou: [{ value: '1', label: "1" }, { value: '2', label: "2" }, { value: '3', label: "3" }, { value: '4', label: "4" }, { value: '5', label: "5" }, { value: '6', label: "6" }, { value: '7', label: "7" }, { value: '8', label: "8" }, { value: '9', label: "9" }, { value: '10', label: "10" }],
            FMoHao: [
                { value: '1', label: "1" }, { value: '2', label: "2" }, { value: '3', label: "3" }, { value: '4', label: "4" }, { value: '5', label: "5" }, { value: '6', label: "6" }, { value: '7', label: "7" }, { value: '8', label: "8" }, { value: '9', label: "9" }, { value: '10', label: "10" },
                { value: '11', label: "11" }, { value: '12', label: "12" }, { value: '13', label: "13" }, { value: '14', label: "14" }, { value: '15', label: "15" },
                { value: 'A', label: "A" }, { value: 'B', label: "B" }, { value: 'C', label: "C" }, { value: 'D', label: "D" }, { value: 'E', label: "E" },
                { value: 'F', label: "F" }, { value: 'G', label: "G" }, { value: 'H', label: "H" }, { value: 'I', label: "I" }
            ],
            FBanBie: [
                { value: 'A', label: "A" }, { value: 'B', label: "B" }, { value: 'C', label: "C" }, { value: 'D', label: "D" }
            ],
            FPanlei: [
                { value: '胶料', label: "胶料" }, { value: '不锈钢', label: "不锈钢" }
            ],
            //用户图片
            FEMPImg: '',
            FEMPImgshow: false,

            // 工位图片
            FSTATIONImg: '',
            FSTATIONImgshow: false,

            // 设备图片
            FMATERIALImg: '',
            FDEVmgshow: false,



            //排程任务表
            gridWaitJobs: {
                props: {
                    pagination: false,
                    enableRangeSelection: true,
                    rowSelection: "single",//multiple  多选
                    rowData: [],
                    total: 0,
                    columnDefs: [
                        {
                            headerName: "",
                            width: 35,
                            filter: false,
                            sortable: false,
                            checkboxSelection: true,
                            // headerCheckboxSelection: true,
                            headerCheckboxSelection: false,//去掉选择列列头的全选
                            pinned: 'left'
                        },
                        {
                            headerName: '$t("排程排序")',
                            field: "SCHEDULE_FSHOW_SEQNO",
                            dataType: "seq",
                            cellRenderer: "baseRenderer",
                            width: 90,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("排程任务编号")',
                            field: "FCRAFT_SCHEDULE_NO",
                            // pinned: 'left',
                            width: 140,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("素材批号")',
                            field: "TAG_ITEM",
                            // pinned: 'left',
                            width: 140,
                        },
                        {
                            headerName: '$t("不良原因")',
                            field: "FBAD_REASON_NAME",
                            editable: false,
                            width: 130,
                            cellRenderer: "agCoreLink",
                            onCellClicked(params) {
                                if (params.data && params.data.FBAD_REASON_NAME) {
                                    hc.ViewModel.openNgItem(params.data);
                                }
                            },
                        },
                        {
                            headerName: '$t("排程任务二维码")',
                            field: "FCRAFT_SCHEDULE_NO",
                            width: 120,
                            cellRenderer: "agCoreLink",
                            onCellClicked(params) {
                                if (params.data.totalRow) return
                                hc.ViewModel.qrcodeDialog.props.title = params.data.FCRAFT_SCHEDULE_NO;
                                hc.ViewModel.qrcodeDialog.props.value = params.data.FCRAFT_SCHEDULE_NO;
                                hc.Controls.qrcodeDialog.show();
                            },
                            cellRendererParams(params) {
                                if (params.data.totalRow) return {}
                                return {
                                    value: hc.ViewModel.$t("排程任务二维码"),
                                };
                            },
                        },
                        {
                            headerName: '$t("加工状态")',
                            field: "FCRAFT_STATUS",
                            width: 120,
                            cellStyle(params) {
                                if (params.data) {
                                    return {
                                        "background-color": hc.ViewModel.platDicDatas_FCRAFT_STATUS[params.data.FCRAFT_STATUS].FDIC_ITEM_CFG.color || '',
                                        color: "#fff",
                                    }
                                }
                            },
                            cellRendererParams(params) {
                                if (params.data) {
                                    return {
                                        value: hc.ViewModel.platDicDatas_FCRAFT_STATUS[params.data.FCRAFT_STATUS].FDIC_ITEM_DESC || '',
                                    }
                                }
                            }
                        },
                        {
                            headerName: '$t("工艺排序")',
                            field: "FSHOW_SEQNO",
                            dataType: "seq",
                            cellRenderer: "baseRenderer",
                            width: 90,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("工艺")',
                            field: "FCRAFT_NAME",
                            width: 120,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("产品编号")',
                            field: "FMATERIAL_CODE",
                            width: 140,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("产品名称")',
                            field: "FMATERIAL_NAME",
                            width: 140,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("规格描述")',
                            field: "FSPEC_DESC",
                            width: 130,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("全部不良数汇总")',
                            field: "BadQty",
                            width: 110,
                            dataType: "number",
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("排程数量")',
                            field: "FPLAN_QTY",
                            width: 110,
                            dataType: "number",
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("合格数量")',
                            field: "FPASS_QTY_SCHEDULE",
                            width: 110,
                            dataType: "number",
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("不良数量")',
                            field: "FNG_QTY_SCHEDULE",
                            width: 110,
                            dataType: "number",
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("实时工时")',
                            field: "FACT_USE_HOUR",
                            width: 110,
                            dataType: "number",
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("计划开始时间")',
                            field: "FPLAN_ST_DATE",
                            width: 130,
                            dataType: "date",
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("实际开始时间")',
                            field: "FACT_ST_DATE",
                            width: 130,
                            dataType: "datetime",
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("计划完成时间")',
                            field: "FPLAN_ED_DATE",
                            width: 130,
                            dataType: "date",
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("工单编号")',
                            field: "FWORK_ORDER_NO",
                            width: 140,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("流程卡号")',
                            field: "FLOT_NO",
                            width: 140,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("订单号")',
                            field: "FIUIU_ORDER_NO",
                            width: 140,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },
                        {
                            headerName: '$t("需求日期")',
                            field: "FIUIU_ORDER_DATE",
                            width: 140,
                            cellStyle(params) {
                                let color = params.data.status == "new" ? '#00FF00' : '#FFFF00'
                                return { color }
                            },
                        },

                        {
                            headerName: '$t("操作")',
                            field: "OPERATE",
                            editable: false,
                            width: 130,
                            cellRenderer: "agCoreLink",
                            onCellClicked(params) {
                                // debugger;
                                hc.ViewModel.waitJobprint();
                                //params.data.OPERATE
                                // return {
                                // }
                            },
                            cellRendererParams(params) {
                                if (params.data) {
                                    let opera = '标签打印';
                                    return {
                                        value: opera,
                                    }
                                }
                            },
                            pinned: 'right',
                            cellStyle(params) {
                                let color = 'green';
                                return { color }
                            },
                        },


                    ],
                },
                events: {
                    name: ["page", "selectionChanged", "rowClicked"],
                    handler: this.gridWaitJobsEventsHander
                }
            },

            //不良明细表
            badgridItem: {
                props: {
                    rowSelection: "multiple",
                    enableRangeSelection: true,
                    rowData: [],
                    total: 0,
                    pagination: false,
                    columnDefs: [
                        {
                            headerName: '$t("不良原因编号")',
                            field: "FBAD_REASON_CODE",
                            width: 130,
                            editable: false,
                        },
                        {
                            headerName: '$t("不良原因名称")',
                            field: "FBAD_REASON_NAME",
                            width: 130,
                            editable: false,
                        },
                        {
                            headerName: '$t("不良数量")',
                            field: "FNG_QTY",
                            width: 130,
                            editable: true,
                            dataType: "qty",
                        },
                        {
                            headerName: '简图1',
                            field: "FATTACH1_ID",
                            editable: false,
                            width: 150,
                            minWidth: 150,
                            cellRenderer: "agCoreImage",
                            cellRendererParams(params) {
                                return {
                                    // 图片地址
                                    src: params.data.FATTACH1_ID && hc.Command.GetAttachmentURL(params.data.FATTACH1_ID),
                                    click() { },
                                };
                            },
                        },
                        {
                            headerName: '简图2',
                            field: "FATTACH2_ID",
                            editable: false,
                            width: 150,
                            minWidth: 150,
                            cellRenderer: "agCoreImage",
                            cellRendererParams(params) {
                                return {
                                    // 图片地址
                                    src: params.data.FATTACH2_ID && hc.Command.GetAttachmentURL(params.data.FATTACH2_ID),
                                    click() { },
                                };
                            },
                        },
                        {
                            headerName: '简图3',
                            field: "FATTACH3_ID",
                            editable: false,
                            width: 150,
                            minWidth: 150,
                            cellRenderer: "agCoreImage",
                            cellRendererParams(params) {
                                return {
                                    // 图片地址
                                    src: params.data.FATTACH3_ID && hc.Command.GetAttachmentURL(params.data.FATTACH3_ID),
                                    click() { },
                                };
                            },
                        },
                        {
                            headerName: '$t("检验人员")',
                            field: "FCHECK_BAD_PERSON_NAME",
                            width: 200,
                            dataType: "text",
                            editable: false,
                        },
                    ],
                },
            },

            // 完工列表
            gridFinishiItem: {
                props: {
                    rowSelection: "multiple", //开启多行
                    enableEditable: true,
                    needNewAddRow: false, //开启允许新增行
                    rowData: [],
                    columnDefs: [
                        {
                            headerName: '$t("排程任务编号")',
                            field: "FCRAFT_SCHEDULE_NO",
                            width: 140,
                            editable: false,
                        },
                        {
                            headerName: '$t("工单编号")',
                            field: "FWORK_ORDER_NO",
                            width: 140,
                            editable: false,
                        },
                        {
                            headerName: '$t("产品编号")',
                            field: "FMATERIAL_CODE",
                            width: 140,
                            editable: false,
                        },
                        {
                            headerName: '$t("产品名称")',
                            field: "FMATERIAL_NAME",
                            width: 140,
                            editable: false,
                        },
                        {
                            headerName: '$t("工艺")',
                            field: "FCRAFT_NAME",
                            width: 120,
                            editable: false,
                        },
                        {
                            headerName: '$t("排程数量")',
                            field: "FPLAN_QTY",
                            width: 110,
                            dataType: "number",
                            editable: false,
                        },
                        {
                            headerName: '$t("机器采集数量")',
                            field: "SKNUM",
                            width: 140,
                            dataType: "number",
                            editable: false,
                        },
                        {
                            headerName: '$t("已完工合格数量")',
                            field: "FPASS_QTY_SCHEDULE",
                            width: 140,
                            dataType: "number",
                            editable: false,
                        },
                        {
                            headerName: '$t("本次合格数量")',
                            field: "FPASS_QTY",
                            width: 140,
                            editable: true,
                            dataType: "number",
                            cellEditorParams(params) {
                                const disabled = hc.ViewModel.centerDialogItemVisible
                                return {
                                    disabled: disabled,
                                    change(record, value) {
                                        if (record.FPASS_QTY === value) { //如果输入的和原来的相等，则不跑下面的逻辑
                                            return false
                                        }
                                        params.data.FPASS_QTY = value
                                        params.data.FPIECES = (params.data.FPASS_QTY / 0.4).toFixed(3)

                                        hc.Controls.gridFinishiItem.refreshRows({
                                            dataRows: [record]
                                        });
                                    },
                                }
                            },
                        },
                        {
                            headerName: '$t("片数")',
                            field: "FPIECES",
                            width: 140,
                            editable: true,
                            dataType: "number",
                            cellEditorParams(params) {
                                const disabled = hc.ViewModel.centerDialogItemVisible
                                return {
                                    disabled: disabled,
                                    change(record, value) {
                                        if (record.FPIECES === value) { //如果输入的和原来的相等，则不跑下面的逻辑
                                            return false
                                        }
                                        params.data.FPASS_QTY = (value * 0.4).toFixed(3)
                                        params.data.FPIECES = value

                                        hc.Controls.gridFinishiItem.refreshRows({
                                            dataRows: [record]
                                        });
                                    },
                                }
                            },
                        },
                        {
                            headerName: '$t("本次不合格数量")',
                            field: "FNG_QTY",
                            width: 140,
                            editable: false,
                            dataType: "number",
                            cellEditorParams(params) {
                                return {
                                    change(record, value) {
                                        if (record.FNG_QTY === value) { //如果输入的和原来的相等，则不跑下面的逻辑
                                            return false
                                        }
                                        params.data.FNG_QTY = value

                                        hc.Controls.gridFinishiItem.refreshRows({
                                            dataRows: [record]
                                        });
                                    },
                                }
                            },
                        },
                        {
                            headerName: '$t("质检日期")',
                            field: "FCHECK_QC_DATE",
                            width: 140,
                            minWidth: 80,
                            editable: true,
                            dataType: "date",
                            precision: 6,
                            cellRenderer: "baseRenderer",
                            rules: [{
                                required: false,
                                validator(rule, value, fn, record, valid) {
                                    if (!value) {
                                        return true;
                                    }
                                    return true;
                                }
                            }]
                        },

                        {
                            headerName: hc.ViewModel.$t('班别'),//"微服务",
                            field: "F_CUSTOM_FIELD2",
                            editable: function (params) {
                                return !hc.Command.IsEmpty(params.data.FBARCODE_NO) ? false : true;
                            },//是否可以编辑
                            width: 180,
                            dataType: "text",

                            cellEditor: "agCoreSelect",
                            cellEditorParams: function (params) {
                                return {
                                    value: params.data.F_CUSTOM_FIELD2,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: hc.ViewModel.FBanBie,
                                    change: function (record, value) {
                                        if (value) {
                                            record.F_CUSTOM_FIELD2 = value;
                                        }
                                        else {
                                            record.F_CUSTOM_FIELD2 = "";
                                        }
                                    }
                                };
                            },
                            cellRenderer: "agCoreRenderLabelByValue",
                            cellRendererParams: function (params) {
                                return {
                                    value: params.data.F_CUSTOM_FIELD2,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: hc.ViewModel.FBanBie,
                                    change: function (record, value) {
                                    }
                                };
                            },

                        },
                        {
                            headerName: '$t("字码/模号")',
                            field: "F_CUSTOM_FIELD3",
                            dataType: "text",
                            width: 120,
                            editable: true,
                        },
                        {
                            headerName: '$t("流水号")',
                            field: "F_CUSTOM_FIELD4",
                            width: 110,
                            dataType: "text",
                            editable: true,
                        },
                        {
                            headerName: hc.ViewModel.$t('材质'),//"微服务",
                            field: "F_CUSTOM_FIELD5",
                            editable: function (params) {
                                return !hc.Command.IsEmpty(params.data.FBARCODE_NO) ? false : true;
                            },//是否可以编辑
                            width: 180,
                            dataType: "text",

                            cellEditor: "agCoreSelect",
                            cellEditorParams: function (params) {
                                return {
                                    value: params.data.F_CUSTOM_FIELD5,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: [{ label: '红铜', value: '红铜' }, { label: '黄铜', value: '黄铜' }, { label: '铁', value: '铁' }],
                                    change: function (record, value) {
                                        if (value) {
                                            record.F_CUSTOM_FIELD5 = value;
                                        }
                                        else {
                                            record.F_CUSTOM_FIELD5 = "";
                                        }
                                    }
                                };
                            },
                            cellRenderer: "agCoreRenderLabelByValue",
                            cellRendererParams: function (params) {
                                return {
                                    value: params.data.F_CUSTOM_FIELD5,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: [{ label: '红铜', value: '红铜' }, { label: '黄铜', value: '黄铜' }, { label: '铁', value: '铁' }],
                                    change: function (record, value) {
                                    }
                                };
                            },

                        },
                    ]
                },
            },

            //属性集合
            // 不合格数量弹窗列表
            ngItem: {
                props: {
                    rowSelection: "multiple", //开启多行
                    enableEditable: true,
                    needNewAddRow: true, //开启允许新增行
                    rowData: [],
                    columnDefs: [
                        {
                            headerName: "",
                            field: "",
                            editable: false,
                            width: 50,
                            forbidEditable: true,
                            dataType: "delete",
                        },
                        {
                            headerName: '$t("本次不合格数量")',
                            field: "FNG_QTY",
                            width: 140,
                            editable: true,
                            dataType: "number",
                            cellEditorParams(params) {
                                return {
                                    change(record, value) {
                                        if (record.FNG_QTY === value) { //如果输入的和原来的相等，则不跑下面的逻辑
                                            return false
                                        }
                                        params.data.FNG_QTY = value
                                        hc.Controls.ngItem.refreshRows({
                                            dataRows: [record]
                                        });
                                    },
                                }
                            },
                        },
                        {
                            headerName: '$t("不良原因")',
                            field: "FBAD_REASON_ID",
                            editable: true,
                            width: 350,
                            cellEditor: "agCoreCascader",
                            cellEditorParams(params) {
                                return {
                                    value: params.data.FBAD_REASON_ID,
                                    valueKey: "ID",
                                    labelKey: "NAME",
                                    childrenKey: "SubCates",
                                    source: hc.ViewModel.BadReasonList || [],
                                };
                            },
                            cellRenderer: "agCoreCascader",
                            cellRendererParams(params) {
                                return {
                                    value: params.data.FBAD_REASON_ID,
                                    valueKey: "ID",
                                    labelKey: "NAME",
                                    childrenKey: "SubCates",
                                    source: hc.ViewModel.BadReasonList || [],
                                    change(record, value, source) {
                                        record.FBAD_REASON_ID = value[value.length - 1]
                                    }
                                };
                            },
                        },
                        {
                            headerName: '$t("检验人员")',
                            field: "FCHECK_BAD_PERSON_NAME",
                            width: 180,
                            editable: false,
                            cellRenderer: "agCoreOpenSearch",
                            cellRendererParams(params) {
                                return {
                                    dataType: "EmployeeOpen",
                                    disabled: false,
                                    readonly: false,
                                    value: params.data.FCHECK_BAD_PERSON_NAME,
                                    dataPropName: "FCHECK_BAD_PERSON_NAME",
                                    afterSelectData(arg1, arg2) {
                                        hc.ViewModel.fillBadReason(arg1, arg2, params.data, 'setBadPerson');
                                    },
                                    clearValue() {
                                        params.data.FCHECK_BAD_PERSON_ID = "";
                                        params.data.FEMP_NAME = "";
                                    },
                                    ifShow: ["totalRow"],
                                };
                            },
                        },

                    ]
                },
                events: {
                    name: ["newAddRow"],
                    handler: hc.ViewModel.ngItemEventHandler,
                },
            },

            // 标签子表
            gridFinishChild: {
                props: {
                    rowSelection: "multiple", //开启多行
                    enableEditable: true,
                    needNewAddRow: true, //开启允许新增行
                    rowData: [],
                    columnDefs: [

                        {
                            headerName: "",
                            field: "",
                            editable: false,
                            width: 50,
                            forbidEditable: true,
                            dataType: "delete",
                            deleteCallback: function (callback, params) {
                                //已保存数据不可删除
                                if (hc.Command.IsEmpty(params.FBARCODE_NO)) {

                                    // 支持异步 统一使用callback 调用callback即删除
                                    hc.Command.DeleteConfirmInEdit(function () {


                                        //调用平台的删除
                                        callback();


                                        let finishItem = hc.ViewModel.gridFinishiItem.props.rowData[0];
                                        //重新计算合格数
                                        finishItem.FPASS_QTY = hc.Controls.gridFinishChild.rowData.filter(item =>
                                            (item.FCRAFT_JOB_BOOKING_TAG_ID == null || item.FCRAFT_JOB_BOOKING_TAG_ID == '') &&
                                            item.FPRODUCT_NUM != null &&
                                            !isNaN(Number(item.FPRODUCT_NUM)) &&
                                            item.FPRODUCT_NUM !== ''
                                        )
                                            .reduce((acc, item) => acc + Number(item.FPRODUCT_NUM), 0);
                                        //片数=合格数/0.4,取小数点后三位
                                        finishItem.FPIECES = (finishItem.FPASS_QTY / 0.4).toFixed(3); // 保留三位小数;

                                        console.log('本次合格数量');
                                        console.log(hc.ViewModel.gridFinishiItem.props.rowData[0].FPASS_QTY);

                                        hc.Controls.gridFinishiItem.refreshRows({
                                            dataRows: hc.ViewModel.gridFinishiItem.props.rowData
                                        });
                                    });
                                }

                            },
                        },
                        {
                            headerName: "",
                            width: 35,
                            filter: false,
                            sortable: false,
                            checkboxSelection: true,
                            headerCheckboxSelection: true,
                        },
                        {
                            headerName: '$t("流水号")',
                            field: "FSHOW_SEQNO",
                            width: 100,
                            editable: false,
                            dataType: "text"
                        },
                        {
                            headerName: '$t("条码编码")',
                            field: "FBARCODE_NO",
                            width: 140,
                            editable: false,
                            dataType: "text"
                        },

                        {
                            headerName: '$t("材料批号")',
                            field: "FSTUFFLOT_NO",
                            width: 140,
                            editable: function (params) {
                                return !hc.Command.IsEmpty(params.data.FBARCODE_NO) ? false : true;
                            },
                            dataType: "text"
                        },
                        {
                            headerName: '$t("数量")',
                            field: "FPRODUCT_NUM",
                            width: 110,
                            editable: function (params) {
                                return !hc.Command.IsEmpty(params.data.FBARCODE_NO) ? false : true;
                            },
                            dataType: "number",
                            cellEditorParams(params) {
                                return {
                                    change(record, value) {
                                        if (record.FPRODUCT_NUM === value) { //如果输入的和原来的相等，则不跑下面的逻辑
                                            return false
                                        }
                                        params.data.FPRODUCT_NUM = value

                                        //可报工数量
                                        let job = hc.ViewModel.gridFinishiItem.props.rowData[0];
                                        let waitQty = job.FPLAN_QTY - job.FFINISH_QTY;

                                        console.log('可报工数量：');
                                        console.log(waitQty);
                                        //毛重
                                        record.FPRODUCT_WEIGHT = (value * hc.ViewModel.MaterialWeight).toFixed(2);

                                        //明细报工总数
                                        let total = hc.Controls.gridFinishChild.rowData.filter(item =>
                                            (item.FCRAFT_JOB_BOOKING_TAG_ID == null || item.FCRAFT_JOB_BOOKING_TAG_ID == '') &&
                                            item.FPRODUCT_NUM != null &&
                                            !isNaN(Number(item.FPRODUCT_NUM)) &&
                                            item.FPRODUCT_NUM !== ''
                                        ).reduce((acc, item) => acc + Number(item.FPRODUCT_NUM), 0);

                                        hc.ViewModel.gridFinishiItem.props.rowData[0].FPASS_QTY = total;
                                        //FPIECES=total/0.4
                                        hc.ViewModel.gridFinishiItem.props.rowData[0].FPIECES = (total / 0.4).toFixed(3);


                                        // // //超出可报工数量
                                        // if (total > waitQty) {
                                        //     hc.ViewModel.gridFinishiItem.props.rowData[0].FPASS_QTY = waitQty;
                                        //     //筛选出gridFinishChild中除了当前行以外的其他行的数量
                                        //     let otherRows = hc.Controls.gridFinishChild.rowData.filter(item => item !== record);
                                        //     let otherTotal = otherRows.reduce((acc, item) => acc + Number(item.FPRODUCT_NUM), 0);
                                        //     //当前行的数量 = 可报工数量 - 其他行的数量
                                        //     value = waitQty - otherTotal;
                                        //     record.FPRODUCT_NUM = waitQty - otherTotal;
                                        //     console.log('当前行数量：');
                                        //     console.log(record.FPRODUCT_NUM);
                                        //     params.data.FPRODUCT_NUM = value


                                        // }
                                        // else {
                                        //     hc.ViewModel.gridFinishiItem.props.rowData[0].FPASS_QTY = total;
                                        // }


                                        hc.Controls.gridFinishiItem.refreshRows({
                                            dataRows: hc.ViewModel.gridFinishiItem.props.rowData
                                        });

                                        hc.Controls.gridFinishChild.refreshRows({
                                            dataRows: hc.ViewModel.gridFinishChild.props.rowData
                                        });




                                    },
                                }
                            },
                        },
                        {
                            headerName: '$t("毛重")',
                            field: "FGROSS_WEIGHT",
                            width: 110,
                            editable: function (params) {
                                return !hc.Command.IsEmpty(params.data.FBARCODE_NO) ? false : true;
                            },
                            dataType: "number",
                            cellEditorParams(params) {
                                return {
                                    change(record, value) {
                                        if (record.FGROSS_WEIGHT === value) { //如果输入的和原来的相等，则不跑下面的逻辑
                                            return false
                                        }
                                        params.data.FGROSS_WEIGHT = value
                                        hc.Controls.gridFinishChild.refreshRows({
                                            dataRows: [record]
                                        });
                                    },
                                }
                            },
                        },
                        {
                            headerName: '$t("净重")',
                            field: "FPRODUCT_WEIGHT",
                            width: 110,
                            editable: false,
                            dataType: "number",
                            cellEditorParams(params) {
                                return {
                                    change(record, value) {
                                        if (record.FPRODUCT_WEIGHT === value) { //如果输入的和原来的相等，则不跑下面的逻辑
                                            return false
                                        }
                                        params.data.FPRODUCT_WEIGHT = value
                                        hc.Controls.gridFinishChild.refreshRows({
                                            dataRows: [record]
                                        });
                                    },
                                }
                            },
                        },
                        {
                            headerName: hc.ViewModel.$t('班别'),//"微服务",
                            field: "FTEXT3",
                            editable: function (params) {
                                return !hc.Command.IsEmpty(params.data.FBARCODE_NO) ? false : true;
                            },//是否可以编辑
                            width: 180,
                            dataType: "text",

                            cellEditor: "agCoreSelect",
                            cellEditorParams: function (params) {
                                return {
                                    value: params.data.FTEXT3,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: hc.ViewModel.FBanBie,
                                    change: function (record, value) {
                                        if (value) {
                                            record.FTEXT3 = value;
                                            // 如果 FSHOW_SEQNO 为空，则自动生成 班别 + 流水号
                                            // 仅当 FCRAFT_JOB_BOOKING_TAG_ID 有值，且 FSHOW_SEQNO 为空时，生成编号
                                            if (!record.FCRAFT_JOB_BOOKING_TAG_ID) {
                                                const allRows = hc.ViewModel.gridFinishChild.props.rowData;

                                                // 筛选出已有的 FSHOW_SEQNO，格式为：班别 + 两位编号
                                                const samePrefixSeq = allRows
                                                    .filter(row => row.FSHOW_SEQNO && row.FSHOW_SEQNO.startsWith(value))
                                                    .map(row => {
                                                        const num = row.FSHOW_SEQNO.replace(value, '');
                                                        return parseInt(num, 10) || 0;
                                                    });

                                                // 获取当前班别的最大编号，进行 +1 生成新流水号
                                                const nextNum = (Math.max(0, ...samePrefixSeq) + 1).toString().padStart(2, '0');
                                                record.FSHOW_SEQNO = value + nextNum;
                                            }
                                        }
                                        else {
                                            record.FTEXT3 = "";
                                        }
                                    }
                                };
                            },
                            cellRenderer: "agCoreRenderLabelByValue",
                            cellRendererParams: function (params) {
                                return {
                                    value: params.data.FTEXT3,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: hc.ViewModel.FBanBie,
                                    change: function (record, value) {
                                    }
                                };
                            },

                        },

                        {
                            headerName: hc.ViewModel.$t('接头'),//"微服务",
                            field: "FTEXT1",
                            editable: function (params) {
                                return !hc.Command.IsEmpty(params.data.FBARCODE_NO) ? false : true;
                            },//是否可以编辑
                            width: 180,
                            dataType: "text",

                            cellEditor: "agCoreSelect",
                            cellEditorParams: function (params) {
                                return {
                                    value: params.data.FTEXT1,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: hc.ViewModel.FJieTou,
                                    change: function (record, value) {
                                        if (value) {
                                            record.FTEXT1 = value;
                                        }
                                        else {
                                            record.FTEXT1 = "";

                                        }
                                    }
                                };
                            },
                            cellRenderer: "agCoreRenderLabelByValue",
                            cellRendererParams: function (params) {
                                return {

                                    value: params.data.FTEXT1,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: hc.ViewModel.FJieTou,
                                    change: function (record, value) {
                                    }
                                };
                            },

                        },
                        {
                            headerName: hc.ViewModel.$t('模号'),//"微服务",
                            field: "FTEXT2",
                            editable: function (params) {
                                return !hc.Command.IsEmpty(params.data.FBARCODE_NO) ? false : true;
                            },//是否可以编辑
                            width: 180,
                            dataType: "text",
                            cellEditor: "agCoreSelect",
                            cellEditorParams: function (params) {
                                return {
                                    value: params.data.FTEXT2,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: hc.ViewModel.FMoHao,
                                    change: function (record, value) {
                                        if (value) {
                                            record.FTEXT2 = value;
                                        }
                                        else {
                                            record.FTEXT2 = "";
                                        }
                                    }
                                };
                            },
                            cellRenderer: "agCoreRenderLabelByValue",
                            cellRendererParams: function (params) {
                                return {
                                    value: params.data.FTEXT2,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: hc.ViewModel.FMoHao,
                                    change: function (record, value) {
                                    }
                                };
                            },

                        },
                        {
                            headerName: hc.ViewModel.$t('盘类'),//"微服务",
                            field: "FTEXT4",
                            editable: function (params) {
                                return !hc.Command.IsEmpty(params.data.FBARCODE_NO) ? false : true;
                            },//是否可以编辑
                            width: 180,
                            dataType: "text",
                            cellEditor: "agCoreSelect",
                            cellEditorParams: function (params) {
                                return {
                                    value: params.data.FTEXT4,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: hc.ViewModel.FPanlei,
                                    change: function (record, value) {
                                        if (value) {
                                            record.FTEXT4 = value;
                                        }
                                        else {
                                            record.FTEXT4 = "";
                                        }
                                    }
                                };
                            },
                            cellRenderer: "agCoreRenderLabelByValue",
                            cellRendererParams: function (params) {
                                return {
                                    value: params.data.FTEXT4,
                                    valueKey: "value",
                                    labelKey: "label",
                                    source: hc.ViewModel.FPanlei,
                                    change: function (record, value) {
                                    }
                                };
                            },

                        },
                    ]
                },
                events: {
                    name: ["newAddRow"],
                    //handler: hc.ViewModel.ngItemEventHandler,
                },
            },

            //报工明细表
            gridJobBooking: {
                props: {
                    gridOptions: {},
                    columnDefs: [{
                        headerName: "",
                        width: 35,
                        filter: false,
                        sortable: false,
                        checkboxSelection: true,
                        headerCheckboxSelection: true,
                        pinned: 'left'
                    },
                    {
                        headerName: '$t("加工状态")',
                        field: "FWORK_STATUS_NAME",
                        editable: false,
                        width: 130,
                        pinned: 'left',
                        cellStyle(params) {
                            if (params.data) {
                                return {
                                    "background-color": hc.ViewModel.platDicDatas_FCRAFT_STATUS[params.data.FWORK_STATUS].FDIC_ITEM_CFG.color || '',
                                    color: "#fff",
                                }
                            }
                        },
                        cellRendererParams(params) {
                            if (params.data) {
                                return {
                                    value: hc.ViewModel.platDicDatas_FCRAFT_STATUS[params.data.FWORK_STATUS].FDIC_ITEM_DESC || '',
                                }
                            }
                        }
                    },
                    // {
                    //     headerName: '$t("OPERATE")',//"操作",
                    //     field: "buttonOrButtonGroup",
                    //     editable: false,
                    //     pinned: 'right',
                    //     width: 120,
                    //     cellRendererParams(params) {
                    //         var edit = {
                    //             text: "修改完工",
                    //             icon: "iconfont icon-eledit",
                    //             type: "primary",
                    //             style: 'margin-right:10px;',
                    //             click(record, item, event) {
                    //                 hc.ViewModel.UpdateJobData = record
                    //                 //如果当前数据行的 FJUAN_STATUS 不等于0 代表已作废过
                    //                 if (record.FJUAN_STATUS !== 0) {
                    //                     hc.ViewModel.FJUAN_STATUS = true
                    //                     hc.ViewModel.cancel_FJUAN_STATUS = true
                    //                 } else {
                    //                     hc.ViewModel.FJUAN_STATUS = false
                    //                     hc.ViewModel.cancel_FJUAN_STATUS = false
                    //                 }
                    //                 hc.ViewModel.UpdateJobDialogVisible = true
                    //             },
                    //         }
                    //         var print = {
                    //             text: "补打标签",
                    //             icon: "iconfont icon-print",
                    //             type: "primary",
                    //             style: 'margin-right:10px;',
                    //             click(record, item, event) {
                    //                 //补打标签，传当前行的排程任务id                                    
                    //                 hc.ViewModel.reimburselable(record.FCRAFT_JOB_BOOKING_ID)
                    //             },
                    //         }

                    //         var editColor = {
                    //             text: "修改颜色",
                    //             icon: "iconfont icon-eledit",
                    //             type: "primary",
                    //             click(record, item, event) {
                    //                 setTimeout(() => {
                    //                     hc.Controls.print.resetLoadingReports();
                    //                 }, 1000); 
                    //                 hc.ViewModel.UpdateColorDialogVisible = true
                    //                 hc.ViewModel.color.FLOT_NO_COLOR = record.FLOT_NO_COLOR

                    //             },
                    //         }
                    //         var allbtn = [edit, print, editColor]
                    //         return {
                    //             source: [print],
                    //         };
                    //     },
                    //     cellRenderer: "agCoreButton",
                    // },
                    {
                        headerName: "$t('订单编号')",
                        editable: false,
                        width: 150,
                        field: "FSALE_ORDER_NO",
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: "$t('工单编号')",
                        editable: false,
                        width: 150,
                        field: "FWORK_ORDER_NO",
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("排程任务编号")',
                        field: "FCRAFT_SCHEDULE_NO",
                        editable: false,
                        width: 150,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("加工任务编号")',
                        field: "FCRAFT_JOB_BOOKING_NO",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: "$t('班别')",
                        editable: false,
                        width: 150,
                        field: "F_CUSTOM_FIELD2",
                    },
                    {
                        headerName: "$t('字码/模号')",
                        editable: false,
                        width: 150,
                        field: "F_CUSTOM_FIELD3",
                    },
                    {
                        headerName: "$t('流水号')",
                        editable: false,
                        width: 150,
                        field: "F_CUSTOM_FIELD4",
                    },
                    {
                        headerName: "$t('材质')",
                        editable: false,
                        width: 150,
                        field: "F_CUSTOM_FIELD5",
                    },
                    {
                        headerName: '$t("产品编号")',
                        field: "FMATERIAL_CODE",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("产品名称")',
                        field: "FMATERIAL_NAME",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("规格描述")',
                        field: "FSPEC_DESC",
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("产品型号")',
                        field: "FGOODS_MODEL",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("产品简图")',
                        field: "FPIC_ATTACH_ID",
                        editable: false,
                        width: 130,
                        cellRenderer: "agCoreImage",
                        cellRendererParams(params) {
                            return {
                                //图片地址
                                src: hc.Command.IsEmpty(params.data.FPIC_ATTACH_ID) ? "" : hc.Command.GetAttachmentURL(params.data.FPIC_ATTACH_ID),
                                click() { },
                                ifShow: ["totalRow"]
                            };
                        },
                    },
                    {
                        headerName: '$t("工艺编号")',
                        field: "FCRAFT_CODE",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("工艺名称")',
                        field: "FCRAFT_NAME",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("工位编号")',
                        field: "FSTATION_CODE",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("工位名称")',
                        field: "FSTATION_NAME",
                        editable: false,
                        width: 150,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("车间编号")',
                        field: "FWORK_SHOP_CODE",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("车间名称")',
                        field: "FWORK_SHOP_NAME",
                        editable: false,
                        width: 150,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("报工人员")',
                        field: "FEMP_NAME",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("实际开工时间")',
                        field: "FACT_ST_DATE",
                        editable: false,
                        dataType: "datetime",
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("实际完工时间")',
                        field: "FACT_ED_DATE",
                        editable: false,
                        dataType: "datetime",
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("实际工时(小时)")',
                        field: "FACT_USE_HOUR",
                        editable: false,
                        width: 140,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("合格数量")',
                        field: "FPASS_QTY",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("不良数量")',
                        field: "FNG_QTY",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("不良原因")',
                        field: "FBAD_REASON_NAME",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("检验人")',
                        field: "FCHECK_PERSON_NAME",
                        editable: false,
                        width: 110,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    },
                    {
                        headerName: '$t("生产单位")',
                        field: "FPRO_UNIT_NAME",
                        editable: false,
                        width: 130,
                        cellStyle: {
                            color: "#FFFF00",
                        }
                    }],
                    rowSelection: "multiple",
                    enableRangeSelection: true,
                    total: 0,
                    pagination: false,
                    rowData: [],
                },
                events: {}
            },

            // ESOP表
            gridESOP: {
                props: {
                    gridOptions: {},
                    columnDefs: [
                        {
                            headerName: '$t("文件名")',
                            field: "FATTACHMENT_NAME",
                            cellRenderer: "agCoreLink",
                            onCellClicked(params) {
                                if (params && params.data && params.data.FATTACHMENT_ID) {
                                    hc.ViewModel.downFile(params.data.FATTACHMENT_ID);
                                }
                            },
                            width: 200,
                        },
                        {
                            headerName: '$t("文件大小")',
                            filed: "ATTACHMENT_SIZE_DESC",
                            width: 130,
                            valueGetter(params) {
                                if (params && params.data) {
                                    let size = params.data.ATTACHMENT_SIZE;
                                    if (size >= 1024 * 1024) {
                                        return hc.Command.Round(size / (1024 * 1024), 2) + " MB";
                                    } else if (size >= 1024) {
                                        return hc.Command.Round(size / (1024), 2) + " KB";
                                    } else if (size >= 0) {
                                        return size + " B";
                                    }
                                }
                                return "";
                            },
                        },
                        {
                            headerName: '$t("简图")',
                            field: "FATTACHMENT_ID",  //FPIC_ATTACH_ID
                            width: 150,
                            minWidth: 150,
                            cellRenderer: "agCoreImage",
                            cellRendererParams(params) {
                                return {
                                    //图片地址
                                    src: params.data.FATTACHMENT_ID && hc.Command.GetAttachmentURL(params.data.FATTACHMENT_ID),
                                    click() { },
                                };
                            },
                        },
                        {
                            headerName: '$t("上传人")',
                            field: "FCREATOR",
                            width: 140,
                        },
                        {
                            headerName: '$t("上传时间")',
                            field: "FCDATE",
                            width: 170,
                            dataType: "datetime",
                        },
                    ],
                    rowSelection: "multiple",
                    enableRangeSelection: true,
                    total: 0,
                    pagination: false,
                    rowData: [],
                },
                events: {
                    name: ["page"],
                    handler: this.gridMstEventsHander
                }
            },

            //巡检单表
            gridChkPatrol: {
                props: {
                    rowSelection: "multiple", //开启多行
                    enableEditable: false,
                    needNewAddRow: false, //开启允许新增行(插入行操作需要)
                    seqFldName: 'FSHOW_SEQNO', // 序号字段名
                    columnDefs: [
                        {
                            headerName: '不良原因编号',
                            field: "FBAD_REASON_CODE",
                            editable: false,
                            width: 150,
                        },
                        {
                            headerName: '不良原因名称',
                            field: "FBAD_REASON_NAME",
                            width: 150,
                            editable: false,
                        },
                        {
                            headerName: '抽检量',
                            field: "FCHK_STIR_QTY",
                            width: 130,
                            dataType: "qty",
                        },
                        {
                            headerName: '不良数量',
                            field: "FNG_QTY",
                            width: 150,
                            dataType: "qty",
                        },
                        {
                            headerName: '简图1',
                            field: "FATTACH1_ID",
                            editable: false,
                            width: 150,
                            minWidth: 150,
                            cellRenderer: "agCoreImage",
                            cellRendererParams(params) {
                                return {
                                    // 图片地址
                                    src: params.data.FATTACH1_ID && hc.Command.GetAttachmentURL(params.data.FATTACH1_ID),
                                    click() { },
                                };
                            },
                        },
                        {
                            headerName: '简图2',
                            field: "FATTACH2_ID",
                            editable: false,
                            width: 150,
                            minWidth: 150,
                            cellRenderer: "agCoreImage",
                            cellRendererParams(params) {
                                return {
                                    // 图片地址
                                    src: params.data.FATTACH2_ID && hc.Command.GetAttachmentURL(params.data.FATTACH2_ID),
                                    click() { },
                                };
                            },
                        },
                        {
                            headerName: '简图3',
                            field: "FATTACH3_ID",
                            editable: false,
                            width: 150,
                            minWidth: 150,
                            cellRenderer: "agCoreImage",
                            cellRendererParams(params) {
                                return {
                                    // 图片地址
                                    src: params.data.FATTACH3_ID && hc.Command.GetAttachmentURL(params.data.FATTACH3_ID),
                                    click() { },
                                };
                            },
                        },
                        {
                            headerName: '备注',
                            field: "F_CUSTOM_FIELD1",
                            width: 130,
                        },
                    ],
                    rowData: [],
                },
                events: {
                    //name: ["newAddRow"],
                    handler: hc.ViewModel.gridItemEventHandler,
                },
            },

            //页面状态控制
            pageStatus: {
                currentPageStatus: "preview", //默认状态
                preview: {
                    //主表
                    gridItem: {
                        enableEditable: true //网格是否可编辑
                    },
                },
                events: { // 翻页
                    name: ["page"],
                    handler: this.pageHandle
                }
            },

            //tabs
            tabsModel: 'WaitJobsPane',

            //主表图片数组属性
            fileList: [],

            // 不良原因
            BadReasonList: [],

            //---暂停原因弹出框属性相关----
            selectBadReasonDiaShow: false,
            FBAD_REASON_ID: '',
            FCHECK_BAD_PERSON_ID: '',//检验人员id

            FBAD_REASON_ID_props: {
                source: [],
                placeholder: dropDownAllowNull,
                filterable: true,
                valueKey: "FBAD_REASON_ID",
                labelKey: "FBAD_REASON_NAME",
            },

            qrcodeDialog: {
                props: {
                    title: "",
                    value: "",
                },
            },

            centerDialogVisibleCount: 0,
        }
    },
    jumps: {},
    created() { },
    init() {
        hc.ViewModel.initPage()//初始化页面方法
        hc.ViewModel.loadData()
    },

    //关闭页面钩子
    beforeDestroy() {
        clearInterval(hc.ViewModel.refreshCheckNewJobTimer) // 查询是否有新任务
    },
    methods: {
        initPage() {
            //获取用户信息
            hc.ViewModel.getUserInfo()

            // 工位图片
            hc.ViewModel.FSTATIONImg = ''
            hc.ViewModel.FSTATIONImgshow = true;

            //产品图片
            hc.ViewModel.FMATERIALImg = ''
            hc.ViewModel.FDEVmgshow = true;

            // hc.Command.ConfirmDialog({
            //     title: "成功",
            //     content: "登录成功，是否全屏使用",
            //     confirm() {
            //         hc.Command.FullScreenThisWindow()
            //     }
            // });

        },

        //获取用户信息
        getUserInfo() {
            hc.Command.LoadData(hc.ViewModel.api.getheadImg, {
                id: hc.UserInfo.UserPsnId
            },
                hc.ViewModel.loadDataSuccess, {
                disableLoading: false,
                el: hc.Controls.gridMst
            });
        },

        //主表回调
        loadDataSuccess(res) {
            if (res.StatusCode == 200) {
                //获取登录用户信息
                if (res.Entity) { //有可能没有用户的具体信息
                    hc.ViewModel.FEMPImg = res.Entity.FATTACHMENT_ID;
                    hc.ViewModel.FEMPImgshow = true;
                }
                hc.ViewModel.User = hc.UserInfo.UserId //UserId:'"0d2af97a1d704ed184355f092e07b897"', UserPsnName: "Guigw"
                hc.ViewModel.userName = hc.UserInfo.UserPsnName

            } else {
                hc.Command.ExceptionDialog({
                    content: res.Message
                });
            }
        },

        //获取打印订单 id
        getPrintIds(param, callBack) {
            if (param.reportDetail.FREPORT_NAME == '素材标签') {
                console.log("123123");
                const selectRows = hc.Controls.gridFinishChild.api.getSelectedRows();
                if (selectRows.length === 0) {
                    hc.Command.WarnDialog({ content: hc.ViewModel.$t('请选择数据行再打印.') });
                    return;
                }
                const ids = selectRows.map(item => item.FCRAFT_JOB_BOOKING_TAG_ID);
                callBack(ids); // 直接调用回调并返回
                return;
            }

            if (param.reportDetail.FREPORT_NAME == '排程卡') {

                // 立即执行的异步函数，确保所有操作完成后才调用回调
                (async () => {
                    try {
                        // 等待颜色保存完成
                        // const saveResult = await hc.ViewModel.UpdateColorDialogSave();
                        // if (!saveResult) {
                        //     return;
                        // }

                        // 获取选中的行
                        const selectRows = hc.Controls.gridJobBooking.api.getSelectedRows();
                        if (selectRows.length === 0) {
                            hc.Command.WarnDialog({ content: hc.ViewModel.$t('请选择数据行再打印.') });
                            return;
                        }

                        // 提取ID并调用回调
                        const ids = selectRows.map(item => item.FCRAFT_JOB_BOOKING_ID);
                        callBack(ids);
                    } catch (error) {
                        console.error('获取打印ID时出错:', error);
                        hc.Command.WarnDialog({ content: hc.ViewModel.$t('获取打印ID时发生错误.') });
                        callBack([]); // 发生异常时返回空数组
                    }
                })();
                return;

            }

            //如果是内箱/外箱
            if (param.reportDetail.FREPORT_NAME == '内箱' || param.reportDetail.FREPORT_NAME == '外箱有抬头' || param.reportDetail.FREPORT_NAME == '外箱无抬头') {
                let ids = [];
                const selectRows = hc.Controls.gridWaitJobs.api.getSelectedRows();
                if (selectRows.length === 0) {
                    hc.Command.WarnDialog({ content: hc.ViewModel.$t('请选择数据行再打印.') });
                    return;
                }
                let model = {
                    PageSize: 9999,
                    PageIndex: 1,
                    WhereGroup: { Groups: [], Items: [], GroupType: "AND" },
                    // "Orders": [
                    //     {
                    //         "Fields": [
                    //             "sch.FCRAFT_SCHEDULE_NO"
                    //         ],
                    //         "OrderType": "Desc"
                    //     },
                    //     {
                    //         "Fields": [
                    //             "wo.FWORK_ORDER_NO"
                    //         ],
                    //         "OrderType": "Desc"
                    //     },
                    //     {
                    //         "Fields": [
                    //             "craft.FSHOW_SEQNO"
                    //         ],
                    //         "OrderType": "Asc"
                    //     },
                    // ]
                };

                model.WhereGroup.Items.push({
                    FieldName: "schedule.FCRAFT_SCHEDULE_ID",
                    OperatorType: "In",
                    Value: selectRows[0].FCRAFT_SCHEDULE_ID,
                });

                if (param.reportDetail.FREPORT_NAME == '内箱') {

                    //内箱
                    model.WhereGroup.Items.push({
                        FieldName: "tag.FPACK_TYPE",
                        OperatorType: "Equal",
                        Value: 'ITEM',
                    });
                }
                else {
                    //外箱
                    model.WhereGroup.Items.push({
                        FieldName: "tag.FPACK_TYPE",
                        OperatorType: "Equal",
                        Value: 'PACK',
                    });
                }

                //请求标签接口
                hc.Command.LoadData(hc.ViewModel.api.GetFinishTagByID, {
                    model: model
                }, (res) => {
                    if (res.StatusCode == 200) {

                        if (res.Entity.length > 0) {
                            ids = res.Entity.map(item => item.FCRAFT_JOB_BOOKING_TAG_ID);
                            callBack(ids); // 直接调用回调并返回
                            return;
                        }
                        else {
                            hc.Command.WarnDialog({ content: hc.ViewModel.$t('没有找到标签数据.') });
                            return;

                        }
                    } else {
                        hc.Command.ShowResMessage(res);
                    }
                })

            }

            // 如果不是已知的报表类型，返回空数组
            //callBack([]);
        },
        // 登出方法
        logOut() {
            hc.Command.ConfirmDialog({
                isTopPop: true,
                content: "确定要退出登录吗？",
                confirm() {
                    if (layer) {
                        // 关闭系统已开弹窗
                        layer.closeAll();
                    }
                    hc.Command.SetRunStatus(0);
                    // 前端登录状态清除
                    hc.Command.RemoveUUID();
                    hc.Command.RemoveUserInfo();
                    // hc.Command.PageRouter.replace({
                    //     path: hc.ViewModel.LoginPageUrl
                    // });
                    hc.Command.ClosePage();
                    window.location.href = hc.Command.GetLoginPageUrl();
                }, //确定的回调方法
            });
        },

        loadData() {
            const para = { //合并请求
                url: [],
                success: hc.ViewModel.loadSuccess,
                error(res) {
                    hc.Command.ExceptionDialog({
                        content: res.Message
                    });
                },
                disableLoading: false, //根据需要，是否显示加载中
            };

            //加载 PublicKey
            para.url.push({
                Name: "GetPublicKey",      //res.Entity的返回结果名，对应为res.Entity.FareaList
                Url: hc.ViewModel.api.initPage,  //请求API
                Paras: {},  //请求参数
            })

            //任务列表接口
            para.url.push({
                Name: "getWaitJobs",
                Url: hc.ViewModel.api.getWaitJobs,
                Paras: {
                    model: {
                        PageSize: 999,
                        PageIndex: 1,
                        WhereGroup: {
                            Groups: [],
                            Items: [
                                { FieldName: "sch.FSTATION_ID", OperatorType: "Equal", Value: hc.ViewModel.mst.FSTATION_ID },
                            ],
                            GroupType: "AND"
                        }
                    },
                },
            })

            //获取不良原因数据源
            para.url.push({
                Name: "getBadReason",
                Url: hc.ViewModel.api.loadBadReasonList,
                Paras: {
                    model: {
                        PageSize: 9999,
                        PageIndex: 1,
                        WhereGroup: {
                            Groups: [],
                            Items: [
                                { FieldName: "FIF_USE", OperatorType: "Equal", Value: true },
                            ],
                            GroupType: "AND"
                        }
                    }
                },
            })

            //获取暂停原因数据源
            para.url.push({
                Name: "getStopReason",
                Url: hc.ViewModel.api.loadBadReasonList,
                Paras: {
                    model: {
                        PageSize: 9999,
                        PageIndex: 1,
                        WhereGroup: {
                            Groups: [],
                            Items: [
                                { FieldName: "FIF_USE", OperatorType: "Equal", Value: true },
                            ],
                            GroupType: "AND"
                        }
                    }
                },
            })

            //获取不良原因分类
            para.url.push({
                Name: "getBadReasonCate",
                Url: hc.ViewModel.api.GetBadReasonCate,
                Paras: {},
            })

            //加载工位树
            para.url.push({
                Name: "stationTree",
                Url: hc.ViewModel.api.getStationTree,
                Paras: null,
            });

            //获取数据字典，排程状态数据  加载内定数据字典 加工状态
            para.url.push({
                Name: "platDicDatas",
                Url: hc.ViewModel.api.getPlatDicByCodesType,
                Paras: {
                    model: {
                        uid: hc.UId,
                        codes: ["BookingWorkStatus", "ScheduleColor"],
                    }
                },
            });

            //内定数据字典
            para.url.push({
                Name: "entDicDatas",
                Url: hc.ViewModel.api.getEntDicByCodesType,
                Paras: {
                    model: {
                        uid: hc.UId,
                        codes: ["ScheduleColor"],
                    }
                },
            });

            //合并请求
            hc.Command.RequestPost(para);
        },
        //loadData 成功回调
        loadSuccess(res) {
            if (res.StatusCode == 200) {
                const { getWaitJobs, getBadReason, getStopReason, getBadReasonCate, platDicDatas, stationTree, GetPublicKey, entDicDatas } = res.Entity

                //调用初始获取时把 任务信息清空
                hc.ViewModel.mst.FCRAFT_SCHEDULE_ID = ''
                hc.ViewModel.HandlingJobs = {}
                hc.ViewModel.FWORK_STATUS = 'original'
                hc.ViewModel.FACT_ST_DATE = ''
                hc.ViewModel.FACT_ED_DATE = ''
                hc.ViewModel.FACT_USE_HOUR = 0

                if (GetPublicKey && GetPublicKey.StatusCode == 200) {
                    hc.ViewModel.PublicKey = "-----BEGIN PUBLIC KEY-----\n" + GetPublicKey.Entity + "\n-----END PUBLIC KEY-----";
                }

                //获取数据字典，排程状态数据
                if (platDicDatas && platDicDatas.StatusCode == 200 && platDicDatas.Entity.length > 0) {
                    const item = platDicDatas.Entity[0].Items
                    //因为状态颜色中存的是json字符串，需要将其转为对象
                    for (let i in item) {
                        if (item[i].FDIC_ITEM_CFG) {
                            item[i].FDIC_ITEM_CFG = JSON.parse(item[i].FDIC_ITEM_CFG)
                        }
                    }
                    hc.ViewModel.platDicDatas_FCRAFT_STATUS = item
                }

                //内定颜色来源
                if (entDicDatas && entDicDatas.StatusCode == 200 && entDicDatas.Entity.length > 0) {

                    entDicDatas.Entity.forEach(item => {

                        if (item.FDIC_SORT_CODE == 'ScheduleColor') {
                            hc.ViewModel.controls.props.FLOT_NO_COLOR.source = item.ListItems
                        }
                    })
                }

                if (getWaitJobs && getWaitJobs.StatusCode == 200) { //排程信息表内容

                    //过滤掉排程任务中的 已完成任务 并将已完工任务调取 结案接口
                    const rowData = getWaitJobs.Entity

                    //创牌要求 排程任务排序
                    hc.ViewModel.gridWaitJobs.props.rowData = hc.ViewModel.sortGridWaitJobs(rowData)

                    //当排程任务刷新时，获取额外的排程任务信息
                    hc.ViewModel.getWaitJobsExtra()

                    //当排程任务刷新时，根据工单获取前工艺总不良数
                    hc.ViewModel.getWaitJobsAllFail()
                }

                // 不良原因 以及不良原因分类 一起合并成层级树
                if (getBadReason && getBadReason.StatusCode == 200
                    && getBadReasonCate && getBadReasonCate.StatusCode == 200
                ) {
                    hc.ViewModel.BadReasonList = hc.ViewModel.formatBadReasonTree(getBadReason.Entity, getBadReasonCate.Entity)
                }

                //获取暂停原因
                if (getStopReason && getStopReason.StatusCode == 200) {
                    hc.ViewModel.FBAD_REASON_ID_props.source = getStopReason.Entity;
                }

                if (stationTree && stationTree.StatusCode == 200) {
                    //工位数据(树形)
                    const data = hc.ViewModel.sortSubCates(stationTree.Entity)
                    hc.ViewModel.stationTree = data;
                }


                //定时查询当前工位是否有新排程任务
                clearInterval(hc.ViewModel.refreshCheckNewJobTimer) //先销毁原来的定时器
                hc.ViewModel.refreshCheckJob()
                hc.ViewModel.refreshCheckNewJobTimer = setInterval(() => { hc.ViewModel.refreshCheckJob() }, 10000)


            } else {
                hc.Command.ExceptionDialog({
                    content: res.Message
                });
            }
        },

        //工位事件
        FSTATION_ID_EventHandler(name, arg1, arg2, arg3) {
            switch (name) {
                case "change":
                    hc.ViewModel.getStationData()
                    break;
            }
        },

        //生产工单事件
        FWORK_ORDER_NO_EventHandler(name, arg1, arg2, arg3) {
            switch (name) {
                case "afterSelectData":
                    if (arg1.selectRows[0].FWORK_ORDER_ID !== hc.ViewModel.mst.FWORK_ORDER_ID) {
                        hc.ViewModel.mst.FWORK_ORDER_ID = arg1.selectRows[0].FWORK_ORDER_ID
                        hc.ViewModel.mst.FWORK_ORDER_NO = arg1.selectRows[0].FWORK_ORDER_NO

                        //选择完生产工单，调用获取排程列表方法
                        hc.ViewModel.loadData()
                    }
                    break;
                case "clearValue":
                    console.log(arg1, arg2)
                    break;
            }
        },

        //每10秒 根据工位id查询一次 是否有新的排程任务，如果返回true 则调取排程任务方法 并播报提示音
        refreshCheckJob() {
            if (hc.ViewModel.mst.FSTATION_ID) {
                hc.Command.LoadData(hc.ViewModel.api.udgeNewScheduleByStationId, {
                    stationId: hc.ViewModel.mst.FSTATION_ID
                }, (res) => {
                    if (res.StatusCode == 200 && res.Entity) { //有数据返回，而且返回的值是 true 则调取获取排程页签的方法
                        hc.Controls.audio.play()
                        setTimeout(() => { hc.Controls.audio.play() }, 12000)//12秒之后再执行一次
                        //如果有新任务下发 将背景颜色改变
                        hc.ViewModel.backColor = 'newJob'
                        //如果有更新，将原来的数据备份一份 以便日后对照是否有更新
                        hc.ViewModel.beforeUpdateJobsData = JSON.parse(JSON.stringify(hc.ViewModel.gridWaitJobs.props.rowData))

                        hc.ViewModel.getWaitJobsData()
                    }
                }, { disableLoading: true })
            }
        },

        //tabs被点击方法
        tabClick(e) {
            if (e.name === 'WaitJobsPane') { //点击第一个表格 排程信息
                hc.ViewModel.getWaitJobsData()
            }
            if (e.name === 'JobBookingPane') { //点击第五个表格 报工明细
                hc.ViewModel.getJobBookingList()
            }
        },

        //扫描排程码、输入排程码
        NumChange() {
            const selectRow = hc.Controls.gridWaitJobs.api.getSelectedRows()
            if (selectRow.length > 0) {
                hc.ViewModel.mst.FCRAFT_SCHEDULE_NO = selectRow[0].FCRAFT_SCHEDULE_NO
            }
            if (!hc.ViewModel.mst.FCRAFT_SCHEDULE_NO) return;

            hc.Command.LoadData(hc.ViewModel.api.getCraftScheduleJob, {
                model: {
                    FQRCODE: hc.ViewModel.mst.FCRAFT_SCHEDULE_NO
                }
            }, res => hc.ViewModel.codeEnterSuccess(res))
        },

        codeEnterSuccess(res) {
            if (res.StatusCode == 200) {
                //---不改变当前的工位信息---
                const { FSTATION_CODE, FSTATION_ID, FSTATION_NAME, FSTATIONImg } = hc.ViewModel.mst
                //---end---
                hc.ViewModel.mst = res.Entity//赋值给主表数据
                //---不改变当前的工位信息---
                hc.ViewModel.mst.FSTATION_CODE = FSTATION_CODE
                hc.ViewModel.mst.FSTATION_ID = FSTATION_ID
                hc.ViewModel.mst.FSTATION_NAME = FSTATION_NAME
                hc.ViewModel.mst.FSTATIONImg = FSTATIONImg
                //---end---

                if (res.Entity.HandlingJobs.length > 0) {
                    hc.ViewModel.HandlingJobs = res.Entity.HandlingJobs[0]
                    hc.ViewModel.FWORK_STATUS = res.Entity.HandlingJobs[0].FWORK_STATUS
                    hc.ViewModel.FACT_ST_DATE = res.Entity.HandlingJobs[0].FACT_ST_DATE
                    hc.ViewModel.FACT_ED_DATE = res.Entity.HandlingJobs[0].FACT_ED_DATE
                    hc.ViewModel.FACT_USE_HOUR = res.Entity.HandlingJobs[0].FACT_USE_HOUR
                } else {
                    hc.ViewModel.HandlingJobs = {}
                    hc.ViewModel.FWORK_STATUS = ''
                    hc.ViewModel.FACT_ST_DATE = ''
                    hc.ViewModel.FACT_ED_DATE = ''
                    hc.ViewModel.FACT_USE_HOUR = 0
                }


                hc.ViewModel.getMaterialData()//获取产品信息，主要是图片信息

                //查询操作之后把背景颜色恢复默认
                hc.ViewModel.backColor = ''

            } else {
                hc.Command.SwitchDialog("warn", hc.Command.StringFormat("{0}", res.Message));
            }
        },

        getMaterialData() {
            return new Promise((resolve, reject) => {
                if (!hc.ViewModel.mst.FSTATION_ID) return reject("无工位ID");

                hc.Command.LoadData(hc.ViewModel.api.getMaterialData, {
                    id: hc.ViewModel.mst.FMATERIAL_ID
                }, (res) => {
                    if (res.StatusCode == 200) {
                        hc.ViewModel.FMATERIALImg = res.Entity.FPIC_ATTACH_ID;
                        hc.ViewModel.FGOODS_MODEL = res.Entity.FGOODS_MODEL;
                        hc.ViewModel.MaterialWeight = res.Entity['MaterialCustom'].FNUMBER_06;
                        resolve();
                    } else {
                        hc.Command.ShowResMessage(res);
                        reject("物料信息获取失败");
                    }
                }, { disableLoading: true });
            });
        },
        //获取物料信息数据
        // getMaterialData() {
        //     if (!hc.ViewModel.mst.FSTATION_ID) return;

        //     hc.Command.LoadData(hc.ViewModel.api.getMaterialData, {
        //         id: hc.ViewModel.mst.FMATERIAL_ID
        //     }, (res) => {
        //         if (res.StatusCode == 200) {
        //             hc.ViewModel.FMATERIALImg = res.Entity.FPIC_ATTACH_ID//获取存货档案图片
        //             hc.ViewModel.FGOODS_MODEL = res.Entity.FGOODS_MODEL//获取存货档案存货描述
        //             hc.ViewModel.MaterialWeight = res.Entity['MaterialCustom'].FNUMBER_06//获取存货档案（端子净重KG/KPCS）

        //             // //标签打印数据
        //             // const row = hc.Controls.gridWaitJobs.api.getSelectedRows();
        //             // let rows = row[0].tagItems
        //             // if (rows.length > 0) {
        //             //     rows.forEach(i => {
        //             //         i.FPRODUCT_WEIGHT = (i.FPRODUCT_NUM * hc.ViewModel.MaterialWeight).toFixed(2)
        //             //     })
        //             //     hc.ViewModel.gridFinishChild.props.rowData = rows
        //             // }

        //         } else {
        //             hc.Command.ShowResMessage(res);
        //         }
        //     }, { disableLoading: true })
        // },

        //获取工位信息数据
        getStationData() {
            if (!hc.ViewModel.mst.FSTATION_ID) return;

            hc.Command.LoadData(hc.ViewModel.api.getStationData, {
                id: hc.ViewModel.mst.FSTATION_ID
            }, (res) => {
                if (res.StatusCode == 200) {
                    hc.ViewModel.FSTATIONImg = res.Entity.FPIC_ATTACH_ID

                    //-------更改工位后，重新赋值设备信息并重新获取数据-----
                    hc.ViewModel.mst.FBOOK_ID = res.Entity.FBOOK_ID
                    hc.ViewModel.FBOOK_CODE = res.Entity.FBOOK_CODE
                    hc.ViewModel.machine = ""// 清空设备信息
                    hc.ViewModel.getDevData() // 同步获取设备信息
                    hc.ViewModel.loadData()
                    //------end----

                } else {
                    hc.Command.ShowResMessage(res);
                }
            })
        },

        //获取产品工艺路线sop ID方法  传入产品id、工艺i、工艺路线id
        getSopPic(arg) {
            const { FMATERIAL_ID } = arg.data
            hc.ViewModel.getSopPicTrue(FMATERIAL_ID);

        },
        //真正获取sop附件方法
        getSopPicTrue(FMATERIAL_ID) {
            let api = hc.ViewModel.api.GetAttachmentListAsync
            let model = {
                PageIndex: 1,
                PageSize: 999,
                WhereGroup: {
                    GroupType: "AND",
                    Groups: [],
                    Items: [
                        {
                            FieldDataType: "",
                            FieldName: "used.FATTACHMENT_FROM_PCODE",
                            OperatorType: "Equal",
                            Value: "MSD002Attach"
                        },
                        {
                            FieldDataType: "",
                            FieldName: "used.FUSED_PK",
                            OperatorType: "Equal",
                            Value: FMATERIAL_ID
                        }
                    ]

                }
            }
            hc.Command.LoadData(api, {
                model
            }, (res) => {
                if (res.StatusCode == 200) {
                    console.log(res)
                    let sopList = res.Entity

                    hc.ViewModel.gridESOP.props.rowData = sopList//赋值到ESOP页签数据
                    //判断是否有 ESOP 数据、判断是否有点击排程任务里的 最新ESOP 预览
                    if (hc.ViewModel.gridESOP.props.rowData && hc.ViewModel.gridESOP.props.rowData.length > 0 && hc.ViewModel.ESOP_click) {
                        hc.ViewModel.showDateDia(hc.ViewModel.gridESOP.props.rowData[0])
                        hc.ViewModel.ESOP_click = false
                    }

                    let sopPreviewList = []
                    for (let i of res.Entity) {
                        if (i.FATTACHMENT_ID) {
                            let a = hc.Command.GetAttachmentURL(i.FATTACHMENT_ID)
                            sopPreviewList.push(a)
                            i.picSrc = hc.Command.GetAttachmentURL(i.FATTACHMENT_ID)
                        }
                    }
                    hc.ViewModel.sopList = sopList//轮播列表
                    hc.ViewModel.sopPreviewList = sopPreviewList//预览数组列表



                } else {
                    hc.ViewModel.ESOP_click = false
                    hc.Command.ExceptionDialog({
                        content: res.Message
                    });
                }
            }, {
                disableLoading: false,
                el: hc.Controls.sopBox
            })

        },

        //获取巡检单页签数据方法
        getChkPatrolData(data) {
            const FMATERIAL_ID = data.data.FMATERIAL_ID;
            if (!FMATERIAL_ID) return;

            hc.Command.LoadData(hc.ViewModel.api.GetMaterialIdAllAsync, {
                materialId: FMATERIAL_ID
            }, (res) => {
                if (res.StatusCode == 200) {

                    for (const i of res.Entity) {
                        let num = 1;
                        if (i.AttachList && i.AttachList.length > 0) {
                            for (const j of i.AttachList) {
                                i[`FATTACH${num}_ID`] = j.FATTACH_ID;
                                num++;
                            }
                        }
                    }
                    hc.ViewModel.gridChkPatrol.props.rowData = res.Entity
                } else {
                    hc.Command.ShowResMessage(res);
                }
            }, { disableLoading: false, el: hc.Controls.gridChkPatrol })
        },

        //获取排程信息表数据方法
        getWaitJobsData() {
            // 记录所选行
            hc.Command.LoadData(hc.ViewModel.api.getWaitJobs, {
                model: {
                    PageSize: 999,
                    PageIndex: 1,
                    WhereGroup: {
                        Groups: [],
                        Items: [
                            { FieldName: "sch.FSTATION_ID", OperatorType: "Equal", Value: hc.ViewModel.mst.FSTATION_ID },
                            { FieldName: "sch.FCRAFT_SCHEDULE_ID", OperatorType: "Equal", Value: hc.ViewModel.mst.FCRAFT_SCHEDULE_ID },
                        ],
                        GroupType: "OR"
                    }
                }
            }, res => {
                if (res.StatusCode == 200) {
                    //过滤掉排程任务中的 已完成任务 并将 已完工的任务 调结案接口
                    const rowData = res.Entity

                    //清空gridJobBooking中的数据
                    hc.ViewModel.gridJobBooking.props.rowData = []

                    if (hc.ViewModel.beforeUpdateJobsData) {
                        //找到新增的排程任务，并将新增的任务 status 字段设置成"new" 
                        for (let i of rowData) {
                            let status = 'new'
                            for (let j of hc.ViewModel.beforeUpdateJobsData) {
                                if (i.FCRAFT_SCHEDULE_ID == j.FCRAFT_SCHEDULE_ID) {
                                    status = 'notNew'
                                    break
                                }
                            }
                            if (status == 'new') {
                                i.status = 'new'
                            } else {
                                i.status = ''
                            }
                        }
                        hc.ViewModel.beforeUpdateJobsData = ''
                    }
                    //创牌要求 排程任务排序
                    const data = hc.ViewModel.sortGridWaitJobs(rowData)
                    hc.ViewModel.gridWaitJobs.props.rowData = data

                    //当排程任务刷新时，获取额外的排程任务信息
                    hc.ViewModel.getWaitJobsExtra()

                    //当排程任务刷新时，根据工单获取前工艺总不良数
                    hc.ViewModel.getWaitJobsAllFail()
                } else {
                    hc.Command.ShowResMessage(res)
                }
            }, { disableLoading: false, el: hc.Controls.outField })
        },


        //获取报工明细表接口
        getJobBookingList() {


            //佳乐要求开窗显示排程信息表
            //hc.ViewModel.btnshow = false;
            const row = hc.Controls.gridWaitJobs.api.getSelectedRows();
            hc.ViewModel.gridFinishiItem.props.rowData = row.length > 0 ? row : []

            //hc.ViewModel.centerDialogVisible = true

            //加载打印下拉
            //hc.ViewModel.UpdateColorDialogVisible = true
            setTimeout(() => {
                hc.Controls.print.resetLoadingReports();
            }, 1000);



            //如果主表没有选择排程任务编号
            if (!hc.ViewModel.mst.FCRAFT_SCHEDULE_NO) {
                return false
            }

            //如果主表中有返回正确的工位ID FSTATION_ID
            if (!hc.ViewModel.mst.FSTATION_ID) {
                return hc.Command.WarnDialog({
                    content: '当前工位错误'
                });
            }

            const model = {
                "JobQRM": {
                    "PageSize": 9999,
                    "PageIndex": 1,
                    "WhereGroup": {
                        "Groups": [
                            { "Items": [] }
                        ],
                        "Items": [{
                            "FieldDataType": "",
                            "FieldName": "FWORK_STATUS",
                            "OperatorType": "Equal",
                            "Value": "finished"
                        }],
                        "GroupType": "AND"
                    },
                    "Orders": [{
                        "Fields": ["FACT_ST_DATE"],
                        "OrderType": 1
                    }]
                },
                "FstationQRM": {
                    "PageSize": 9999,
                    "PageIndex": 1,
                    "WhereGroup": {
                        "Groups": [{
                            "Items": [
                                {
                                    "FieldName": "FSTATION_ID",
                                    "FieldDataType": "",
                                    "OperatorType": "Like",
                                    "Value": hc.ViewModel.mst.FSTATION_ID
                                },
                            ]
                        }],
                        "Items": [], "GroupType": "AND"
                    }
                },
                "ScheduleQRM": {
                    "PageSize": 9999,
                    "PageIndex": 1,
                    "WhereGroup": {
                        "Groups": [{
                            "Items": [
                                {
                                    "FieldName": "FCRAFT_SCHEDULE_NO",
                                    "FieldDataType": "",
                                    "OperatorType": "Like",
                                    "Value": hc.ViewModel.mst.FCRAFT_SCHEDULE_NO
                                },
                            ]
                        }],
                        "Items": [], "GroupType": "AND"
                    }
                }
            }
            hc.Command.LoadData(hc.ViewModel.api.getList, {
                model
            }, (res) => {
                if (res.StatusCode == 200) {
                    hc.ViewModel.gridJobBooking.props.rowData = res.Entity
                } else {
                    hc.Command.ShowResMessage(res)
                }
            }, {
                disableLoading: false,
                el: hc.Controls.gridJobBooking
            })
        },


        // 开工
        startclick() {
            if (!hc.ViewModel.mst.FSTATION_ID) {
                hc.ViewModel.$message.error("请先选择工位");
                return;
            }

            const rows = hc.Controls.gridWaitJobs.api.getSelectedRows();
            if (rows.length == 0) {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("请选择任务进行开工!")
                })
            }

            hc.Command.ConfirmDialog({
                title: "提示",
                content: "确定要开工吗?",
                confirm() {
                    hc.ViewModel.QueryStartJobAsync(); //请求接口数据
                }
            });
        },


        //点击开工请求
        QueryStartJobAsync() {
            const rows = hc.Controls.gridWaitJobs.api.getSelectedRows();
            const rowsData = []
            for (let i of rows) {
                if (rows[0].FCRAFT_ID != i.FCRAFT_ID) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("加工工艺必须一致!")
                    })
                }
                if (i.FCRAFT_STATUS == "unstart" || i.FCRAFT_STATUS == "cancel" || i.FCRAFT_STATUS == "partfinished") {
                    const data = {}
                    data.OperateType = "start"
                    data.FCRAFT_SCHEDULE_ID = i.FCRAFT_SCHEDULE_ID// 任务id
                    data.FSTATION_ID = hc.ViewModel.mst.FSTATION_ID//工位id
                    rowsData.push(data)
                } else {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("任务加工状态必须为未开工或者取消开工或者部分完工!")
                    })
                }
            }

            hc.Command.LoadData(hc.ViewModel.api.StartJobAsync, {
                models: rowsData
            }, hc.ViewModel.QueryStartJobAsyncSuccess, { disableLoading: false });
        },
        QueryStartJobAsyncSuccess(res) {
            if (res.StatusCode == 200) {

                hc.ViewModel.HandlingJobs = res.Entity[0]
                hc.ViewModel.FWORK_STATUS = res.Entity[0].FWORK_STATUS
                hc.ViewModel.FACT_ST_DATE = res.Entity[0].FACT_ST_DATE
                hc.ViewModel.FACT_USE_HOUR = res.Entity[0].FACT_USE_HOUR

                // 获取排程数据
                hc.ViewModel.getWaitJobsData();
            } else {
                hc.Command.ShowResMessage(res);
            }
        },

        //取消开工
        cancelclick() {
            if (!hc.ViewModel.mst.FSTATION_ID) {
                hc.ViewModel.$message.error("请先选择工位");
                return;
            }

            const row = hc.Controls.gridWaitJobs.api.getSelectedRows();

            if (row.length == 0) {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("请选择任务进行取消开工!")
                })
            } else if (row.length > 1) {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("只能选择一条任务取消开工!")
                })
            }

            //如果FWORK_STATUS不等于working时，不能取消  必须有任务id
            if (row[0].FCRAFT_STATUS !== 'working') {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("只有加工任务的加工状态为加工中才可以取消开工!")
                })
            }

            hc.Command.ConfirmDialog({
                title: "提示",
                content: "确定要取消开工吗?",
                confirm() {
                    hc.ViewModel.QueryCancelJobAsync(); //请求接口数据
                }
            });
        },
        QueryCancelJobAsync() {
            hc.Command.LoadData(hc.ViewModel.api.CancelJobAsync, {
                model: {
                    OperateType: "cancel",
                    FCRAFT_JOB_BOOKING_ID: hc.ViewModel.HandlingJobs.FCRAFT_JOB_BOOKING_ID
                }
            },
                hc.ViewModel.QueryCancelJobAsyncSuccess, { disableLoading: false });
        },
        QueryCancelJobAsyncSuccess(res) {
            if (res.StatusCode == 200) {

                hc.ViewModel.HandlingJobs = res.Entity
                hc.ViewModel.FWORK_STATUS = res.Entity.FWORK_STATUS
                hc.ViewModel.FACT_ST_DATE = res.Entity.FACT_ST_DATE
                hc.ViewModel.FACT_USE_HOUR = res.Entity.FACT_USE_HOUR

                hc.ViewModel.getWaitJobsData();
            } else {
                hc.Command.ShowResMessage(res);
            }
        },

        //结案
        endClose() {
            if (!hc.ViewModel.mst.FSTATION_ID) {
                hc.ViewModel.$message.error("请先选择工位");
                return;
            }

            let row = hc.Controls.gridWaitJobs.api.getSelectedRows();
            if (row.length == 0) {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("请选择任务进行结案!")
                })
            } else if (row.length > 1) {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("只能选择一条任务结案!")
                })
            }
            hc.Command.ConfirmDialog({
                title: "提示",
                content: "确定要结案吗?",
                confirm() {
                    hc.ViewModel.QueryEndCloseJobAsync(row); //请求接口数据
                }
            });
        },
        QueryEndCloseJobAsync(row) {
            hc.Command.LoadData(hc.ViewModel.api.endClose, {
                ids: [row[0].FCRAFT_SCHEDULE_ID]//这里传的是  FCRAFT_SCHEDULE_ID
            }, hc.ViewModel.QueryEndCloseJobAsyncSuccess, { disableLoading: false });
        },
        QueryEndCloseJobAsyncSuccess(res) {
            if (res.StatusCode == 200) {

                hc.ViewModel.HandlingJobs = {}
                hc.ViewModel.FWORK_STATUS = 'original'
                hc.ViewModel.FACT_ST_DATE = ''
                hc.ViewModel.FACT_USE_HOUR = 0

                hc.ViewModel.getWaitJobsData();
            } else {
                hc.Command.ShowResMessage(res);
            }
        },

        //暂停
        pauseClick() {

            if (!hc.ViewModel.mst.FSTATION_ID) {
                hc.ViewModel.$message.error("请先选择工位");
                return;
            }

            const row = hc.Controls.gridWaitJobs.api.getSelectedRows();
            if (row.length == 0) {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("请选择任务进行暂停!")
                })
            }

            hc.Command.ConfirmDialog({
                title: "提示",
                content: "确定要暂停吗?",
                confirm() {
                    hc.ViewModel.QueryPauseJobAsync(); //请求接口数据
                }
            });
        },
        QueryPauseJobAsync() {
            const rows = hc.Controls.gridWaitJobs.api.getSelectedRows();
            const rowsData = []
            for (let i of rows) {
                if (rows[0].FCRAFT_ID != i.FCRAFT_ID) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("加工工艺必须一致!")
                    })
                }
                if (i.FCRAFT_STATUS == "working") {
                    const data = {}
                    data.OperateType = "pause"
                    // data.FCRAFT_JOB_BOOKING_ID = i.HandlingJobs[0].FCRAFT_JOB_BOOKING_ID// 任务id
                    data.FCRAFT_JOB_BOOKING_ID = hc.ViewModel.HandlingJobs.FCRAFT_JOB_BOOKING_ID// 任务id
                    rowsData.push(data)
                } else {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("任务加工状态必须为加工中!")
                    })
                }
            }
            hc.Command.LoadData(hc.ViewModel.api.PauseJobAsync, {
                models: rowsData
            },
                hc.ViewModel.QueryPauseJobAsyncSuccess, { disableLoading: false });
        },
        QueryPauseJobAsyncSuccess(res) {
            if (res.StatusCode == 200) {

                hc.ViewModel.HandlingJobs = res.Entity[0]
                hc.ViewModel.FWORK_STATUS = res.Entity[0].FWORK_STATUS
                hc.ViewModel.FACT_ST_DATE = res.Entity[0].FACT_ST_DATE
                hc.ViewModel.FACT_USE_HOUR = res.Entity[0].FACT_USE_HOUR

                hc.ViewModel.getWaitJobsData();
            } else {
                hc.Command.ShowResMessage(res);
            }
        },

        //恢复
        resumeClick() {
            if (!hc.ViewModel.mst.FSTATION_ID) {
                hc.ViewModel.$message.error("请先选择工位");
                return;
            }

            const row = hc.Controls.gridWaitJobs.api.getSelectedRows();
            if (row.length == 0) {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("请选择任务进行恢复!")
                })
            }

            hc.Command.ConfirmDialog({
                title: "提示",
                content: "确定要恢复吗?",
                confirm() {
                    hc.ViewModel.QueryResumeJobAsync(); //请求接口数据
                }
            });
        },
        QueryResumeJobAsync() {
            const rows = hc.Controls.gridWaitJobs.api.getSelectedRows();
            const rowsData = []
            for (let i of rows) {
                if (rows[0].FCRAFT_ID != i.FCRAFT_ID) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("加工工艺必须一致!")
                    })
                }
                if (i.FCRAFT_STATUS == "paused") {
                    const data = {}
                    data.OperateType = "resume"
                    data.FCRAFT_JOB_BOOKING_ID = hc.ViewModel.HandlingJobs.FCRAFT_JOB_BOOKING_ID// 任务id
                    rowsData.push(data)
                } else {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("任务加工状态必须为暂停!")
                    })
                }
            }
            hc.Command.LoadData(hc.ViewModel.api.ResumeJobAsync, {
                models: rowsData
            }, hc.ViewModel.QueryResumeJobAsyncSuccess, { disableLoading: false });
        },
        QueryResumeJobAsyncSuccess(res) {
            if (res.StatusCode == 200) {

                hc.ViewModel.HandlingJobs = res.Entity[0]
                hc.ViewModel.FWORK_STATUS = res.Entity[0].FWORK_STATUS
                hc.ViewModel.FACT_ST_DATE = res.Entity[0].FACT_ST_DATE
                hc.ViewModel.FACT_USE_HOUR = res.Entity[0].FACT_USE_HOUR

                hc.ViewModel.getWaitJobsData();

            } else {
                hc.Command.ShowResMessage(res);
            }
        },

        //不良原因编号和检验人员
        fillBadReason(arg, agr2, gridBadReasonRenderDataRow, setBadType) {
            if (!hc.Command.IsEmpty(arg) && !hc.Command.IsEmpty(arg.selectRows) && arg.selectRows.length > 0) {
                let newRows = [];
                let fillOne = false;
                if (!hc.Command.IsEmpty(gridBadReasonRenderDataRow)) {
                    //优先填充当前行,其他行追加
                    let selRow = arg.selectRows[0];
                    let gridRow = gridBadReasonRenderDataRow;
                    hc.ViewModel[setBadType](gridRow, selRow);

                    fillOne = true;

                    hc.Controls.ngItem.setRowDataEdit(gridRow);

                    hc.Controls.ngItem.refreshRows({
                        dataRows: [Object.assign({}, gridBadReasonRenderDataRow)],
                        redraw: true
                    });
                    //移除最后一个空行
                    if (arg.selectRows.length > 1) {
                        if (hc.ViewModel.ngItem.props.rowData.indexOf(gridRow) != hc.ViewModel.ngItem.props.rowData.length - 1 &&
                            hc.ViewModel.ngItem.props.rowData.length > 0) {
                            hc.ViewModel.ngItem.props.rowData.splice(hc.ViewModel.ngItem.props.rowData.length - 1, 1);
                        }
                    }
                    newRows.push(gridRow);
                }
                //选取的其他数据行追加到网格下面
                let firstIndex = (fillOne) ? 1 : 0;
                if (arg.selectRows.length > firstIndex) {
                    for (r = firstIndex; r < arg.selectRows.length; r++) {
                        let selRow = arg.selectRows[r];
                        hc.Controls.ngItem.addNewRowData((newRow) => {
                            hc.ViewModel[setBadType](newRow, selRow);
                            newRows.push(newRow);

                        });
                    }
                    //最后追加一个新行
                    hc.Controls.ngItem.addNewRow();
                }
            }
        },

        //设置不良人员行值内容
        setBadPerson(row, badPersonRow) {
            row.FCHECK_BAD_PERSON_ID = badPersonRow.FEMP_ID;
            row.FCHECK_BAD_PERSON_NAME = badPersonRow.FEMP_NAME;
        },

        //打印点击
        waitJobprint() {

            //禁止新增行
            hc.ViewModel.gridFinishChild.props.needNewAddRow = false;
            const row = hc.Controls.gridWaitJobs.api.getSelectedRows();

            hc.ViewModel.gridFinishiItem.props.rowData = row

            hc.ViewModel.centerDialogVisible = true
            hc.ViewModel.btnshow = false;



            setTimeout(() => {
                hc.Controls.print.resetLoadingReports();
            }, 1000);


            const selectData = hc.Controls.gridWaitJobs.api.getSelectedRows();
            if (hc.ViewModel?.FBOOK_CODE && hc.ViewModel.FBOOK_CODE.split("-").length > 1) {
                hc.Command.LoadData(hc.ViewModel.api.GetSkApi, {
                    model: {
                        CODE: hc.ViewModel.FBOOK_CODE.split("-")[1],
                        START: selectData.FPLAN_ST_DATE
                    }
                }, (res) => {
                    selectData[0].SKNUM = res.Entity.data[0].nums
                })

            }

            if (row[0].FIF_TAG == true && row[0].FTAG_TYPE != '' && row[0].FTAG_REPORTID != '') {
                hc.ViewModel.centerDialogItemVisible = true
            }

            hc.ViewModel.gridFinishChild.props.rowData = !hc.Command.IsEmpty(row[0].tagItems) ? row[0].tagItems : [];
        },

        //修改颜色
        UpdateColorDialogSave() {
            return new Promise((resolve) => {
                hc.Controls['colorForm'].validate(async (valid, a) => {
                    if (valid) {
                        let selectRows = hc.Controls.gridJobBooking.api.getSelectedRows();

                        if (selectRows.length === 0) {
                            hc.Command.WarnDialog({ content: hc.ViewModel.$t('请选择数据行.') });
                            resolve(false);
                            return;
                        }

                        let model = {
                            FCRAFT_SCHEDULE_ID: selectRows[0].FCRAFT_SCHEDULE_ID,
                            FLOT_NO_COLOR: hc.ViewModel.color.FLOT_NO_COLOR
                        };

                        try {
                            // 使用Promise封装异步请求
                            const res = await new Promise((innerResolve) => {
                                hc.Command.LoadData(hc.ViewModel.api.saveColor, {
                                    model: model
                                }, (res) => {
                                    innerResolve(res);
                                });
                            });

                            if (res.StatusCode == 200) {


                                //更新gridJobBooking中的颜色字段
                                let data = hc.ViewModel.gridJobBooking.props.rowData;
                                data.forEach((row) => {
                                    if (row.FCRAFT_SCHEDULE_ID === selectRows[0].FCRAFT_SCHEDULE_ID) {
                                        row.FLOT_NO_COLOR = hc.ViewModel.color.FLOT_NO_COLOR;
                                    }
                                });
                                hc.ViewModel.gridJobBooking.props.rowData = data;

                                hc.Controls.gridJobBooking.refreshRows({
                                    dataRows: data
                                });

                                // 返回成功提示
                                hc.Command.NotifyDialog({
                                    content: res.Message
                                });
                                resolve(true); // 保存成功
                            } else {
                                hc.Command.ShowResMessage(res);
                                resolve(false); // 保存失败
                            }
                        } catch (error) {
                            console.error('保存颜色时出错:', error);
                            hc.Command.WarnDialog({ content: hc.ViewModel.$t('保存颜色时发生错误.') });
                            resolve(false); // 保存失败
                        }
                    } else {
                        resolve(false); // 验证失败
                    }
                });
            });
        },


        //完工
        async CompletionDeclaration() {
            // 设置为部分完工模式
            hc.ViewModel.dialogTitle = "部分完工报工";
            hc.ViewModel.operationType = "finish";

            hc.ViewModel.company == "JL"
            //恢复新增行
            hc.ViewModel.gridFinishChild.props.needNewAddRow = true;
            hc.ViewModel.btnshow = true;

            console.log(hc.ViewModel.mst)
            if (!hc.ViewModel.mst.FSTATION_ID) {
                hc.ViewModel.$message.error("请先选择工位");
                return;
            }

            const row = hc.Controls.gridWaitJobs.api.getSelectedRows();
            hc.ViewModel.gridFinishChild.props.rowData = [];

            for (let i of row) {
                if (row[0].FCRAFT_ID != i.FCRAFT_ID) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("加工工艺必须一致!")
                    })
                }
                if (row[0].FACT_ST_DATE !== i.FACT_ST_DATE) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("选中的排程任务必须开工时间一致!")
                    })
                }
                //如果FWORK_STATUS不等于working时，不能取消  必须有任务id
                if (i.FCRAFT_STATUS !== 'working') {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("只有加工任务的加工状态为加工中才可以部分完工!")
                    })
                }
            }


            if (row.length == 0) {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("请选择任务进行部分完工!")
                })
            }
            const selectData = hc.Controls.gridWaitJobs.api.getSelectedRows();
            if (hc.ViewModel?.FBOOK_CODE && hc.ViewModel.FBOOK_CODE.split("-").length > 1) {
                hc.Command.LoadData(hc.ViewModel.api.GetSkApi, {
                    model: {
                        CODE: hc.ViewModel.FBOOK_CODE.split("-")[1],
                        START: selectData.FPLAN_ST_DATE
                    }
                }, (res) => {
                    selectData[0].SKNUM = res.Entity.data[0].nums
                })

            }

            console.log('工位id' + hc.ViewModel.mst.FSTATION_ID);
            console.log('排程id' + selectData[0].FCRAFT_SCHEDULE_ID);


            const fun = async (qty) => {
                hc.ViewModel.centerDialogVisible = true

                try {

                    hc.Controls.dialogSubmit.$nextTick(async () => {

                        // if (row[0].tagItems?.length > 0 && hc.ViewModel.MaterialWeight > 0) {
                        //     row[0].tagItems.forEach(i => {
                        //         // 使用已加载的物料重量进行计算
                        //         i.FPRODUCT_WEIGHT = (i.FPRODUCT_NUM * hc.ViewModel.MaterialWeight).toFixed(2)
                        //     });
                        // }

                        //复制排程表中的标签数据，
                        let lableinfo = [];
                        if (!hc.Command.IsEmpty(row[0].tagItems)) {
                            lableinfo = JSON.parse(JSON.stringify(row[0].tagItems));
                        }
                        hc.ViewModel.gridFinishChild.props.rowData = lableinfo;

                        console.log('标签数据')
                        console.log(lableinfo)
                        hc.ViewModel.gridFinishiItem.props.rowData = row

                    });
                } catch (error) {
                    hc.ViewModel.$message.error("获取物料重量失败，请重试");
                    return;
                }


                if (row[0].FIF_TAG == true && row[0].FTAG_TYPE != '' && row[0].FTAG_REPORTID != '') {
                    hc.ViewModel.centerDialogItemVisible = true
                }
                else {
                    hc.ViewModel.centerDialogItemVisible = false
                }
                row.forEach(i => {
                    let passqty = 0
                    if (hc.ViewModel.machine && hc.ViewModel.company != "JL") {
                        passqty = qty || 0
                    } else {
                        if (hc.ViewModel.centerDialogItemVisible == true) {
                            passqty = 0
                        } else {
                            passqty = i.FPLAN_QTY - i.FFINISH_QTY_SCHEDULE
                        }

                    }
                    i.FPASS_QTY = passqty;
                    //片数=合格数/0.4,取小数点后三位
                    i.FPIECES = (i.FPASS_QTY / 0.4).toFixed(3); // 保留三位小数;

                    i.FNG_QTY = 0;
                    i.FSPLIT_AND_FLOW = true
                });



                //延时执行
                hc.Controls.dialogSubmit.$nextTick(() => {
                    setTimeout(() => {
                        hc.Controls.print.resetLoadingReports();
                        hc.Controls.gridFinishiItem.refreshRows({
                            dataRows: [row]
                        });
                    }, 1000);
                });



                //打开部分完工弹窗时，去除不合格数量弹窗的内容
                hc.ViewModel.ngItem.props.rowData = []
            }
            if (hc.ViewModel.company == "JL") {
                fun();
            } else {
                // 获取工位机完成数量
                hc.Command.LoadData(hc.ViewModel.api.getCounterByStation, {
                    machine: hc.ViewModel.machine
                }, (res) => {
                    fun((res.Entity?.COUNTING_QUANTITY || 0) - (res.Entity?.OFFSET_QUANTITY || 0))
                    if (res.StatusCode != 200) {
                        hc.Command.ShowResMessage(res)
                    }
                })
            }
        },
        // 批量拆分完工申报
        async BatchSplitDeclaration() {
            // 设置为批量拆分模式
            hc.ViewModel.dialogTitle = "批量拆分完工";
            hc.ViewModel.operationType = "split";

            hc.ViewModel.company == "JL"
            //恢复新增行
            hc.ViewModel.gridFinishChild.props.needNewAddRow = true;
            hc.ViewModel.btnshow = true;

            console.log(hc.ViewModel.mst)
            if (!hc.ViewModel.mst.FSTATION_ID) {
                hc.ViewModel.$message.error("请先选择工位");
                return;
            }

            const row = hc.Controls.gridWaitJobs.api.getSelectedRows();
            hc.ViewModel.gridFinishChild.props.rowData = [];

            for (let i of row) {
                if (row[0].FCRAFT_ID != i.FCRAFT_ID) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("加工工艺必须一致!")
                    })
                }
                if (row[0].FACT_ST_DATE !== i.FACT_ST_DATE) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("选中的排程任务必须开工时间一致!")
                    })
                }
                //如果FWORK_STATUS不等于working时，不能取消  必须有任务id
                if (i.FCRAFT_STATUS !== 'working') {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("只有加工任务的加工状态为加工中才可以进行批量拆分!")
                    })
                }
            }

            if (row.length == 0) {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("请选择任务进行批量拆分!")
                })
            }

            const selectData = hc.Controls.gridWaitJobs.api.getSelectedRows();
            if (hc.ViewModel?.FBOOK_CODE && hc.ViewModel.FBOOK_CODE.split("-").length > 1) {
                hc.Command.LoadData(hc.ViewModel.api.GetSkApi, {
                    model: {
                        CODE: hc.ViewModel.FBOOK_CODE.split("-")[1],
                        START: selectData.FPLAN_ST_DATE
                    }
                }, (res) => {
                    selectData[0].SKNUM = res.Entity.data[0].nums
                })
            }

            console.log('工位id' + hc.ViewModel.mst.FSTATION_ID);
            console.log('排程id' + selectData[0].FCRAFT_SCHEDULE_ID);

            const fun = async (qty) => {
                hc.ViewModel.centerDialogVisible = true

                try {
                    hc.Controls.dialogSubmit.$nextTick(async () => {
                        if (row[0].tagItems?.length > 0 && hc.ViewModel.MaterialWeight > 0) {
                            row[0].tagItems.forEach(i => {
                                // 使用已加载的物料重量进行计算
                                i.FPRODUCT_WEIGHT = (i.FPRODUCT_NUM * hc.ViewModel.MaterialWeight).toFixed(2)
                            });
                        }

                        //复制排程表中的标签数据，
                        let lableinfo = [];
                        if (!hc.Command.IsEmpty(row[0].tagItems)) {
                            lableinfo = JSON.parse(JSON.stringify(row[0].tagItems));
                        }
                        hc.ViewModel.gridFinishChild.props.rowData = lableinfo;

                        console.log('标签数据')
                        console.log(lableinfo)
                        hc.ViewModel.gridFinishiItem.props.rowData = row
                    });
                } catch (error) {
                    hc.ViewModel.$message.error("获取物料重量失败，请重试");
                    return;
                }

                if (row[0].FIF_TAG == true && row[0].FTAG_TYPE != '' && row[0].FTAG_REPORTID != '') {
                    hc.ViewModel.centerDialogItemVisible = true
                }
                else {
                    hc.ViewModel.centerDialogItemVisible = false
                }

                row.forEach(i => {
                    let passqty = 0
                    if (hc.ViewModel.machine && hc.ViewModel.company != "JL") {
                        passqty = qty || 0
                    } else {
                        if (hc.ViewModel.centerDialogItemVisible == true) {
                            passqty = 0
                        } else {
                            passqty = i.FPLAN_QTY - i.FFINISH_QTY_SCHEDULE
                        }
                    }
                    i.FPASS_QTY = passqty;
                    i.FNG_QTY = 0;
                    // 设置拆分标识和拆分模式
                    i.FSPLIT_AND_FLOW = true;
                    i.FSPLIT_MODE = "total_finished"; // 使用默认模式：基于当前合格数量拆分
                });

                //延时执行
                hc.Controls.dialogSubmit.$nextTick(() => {
                    setTimeout(() => {
                        hc.Controls.print.resetLoadingReports();
                        hc.Controls.gridFinishiItem.refreshRows({
                            dataRows: [row]
                        });
                    }, 1000);
                });

                //打开批量拆分弹窗时，去除不合格数量弹窗的内容
                hc.ViewModel.ngItem.props.rowData = []
            }

            if (hc.ViewModel.company == "JL") {
                fun();
            } else {
                // 获取工位机完成数量
                hc.Command.LoadData(hc.ViewModel.api.getCounterByStation, {
                    machine: hc.ViewModel.machine
                }, (res) => {
                    fun((res.Entity?.COUNTING_QUANTITY || 0) - (res.Entity?.OFFSET_QUANTITY || 0))
                    if (res.StatusCode != 200) {
                        hc.Command.ShowResMessage(res)
                    }
                })
            }
        },
        submitform() { //完工弹窗提交            
            const row = hc.ViewModel.gridFinishiItem.props.rowData;
            let context = "<br>";
            for (let i of row) {
                if (hc.Command.IsEmpty(i.FPASS_QTY)) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("合格数量不能为空!")
                    })
                }
                if (hc.Command.IsEmpty(i.FNG_QTY)) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("不合格数量不能为空!")
                    })
                }
                if (i.FPASS_QTY + i.FNG_QTY == 0) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("合格数量以及不合格数量不能都为0!")
                    })
                }

                //已完工的排程
                const selectData = hc.Controls.gridWaitJobs.api.getSelectedRows();
                //已完工数量
                const endOrder = selectData[0].FNG_QTY_SCHEDULE + selectData[0].FPASS_QTY_SCHEDULE;
                //如果当前报工良+当前报工良品不良+已完工的不良+已完工良品 > 排程数量，触发提示
                if ((i.FPASS_QTY + i.FNG_QTY) + endOrder > i.FPLAN_QTY) {
                    const moreThanQty = (i.FPASS_QTY + i.FNG_QTY) - (i.FPLAN_QTY - endOrder)
                    context += `本次合格数量：${i.FPASS_QTY}<br/>
                                本次不合格数量：${i.FNG_QTY}<br/>
                                <span style="color:${hc.Config.dataColor.warn}">超出数量：${moreThanQty}</span><br/>
                                加总已超出排程任务：${selectData[0].FCRAFT_SCHEDULE_NO}， 
                                <span style="color:${hc.Config.dataColor.warn}">未完工数量：${i.FPLAN_QTY - endOrder}</span>
                                `;
                }
                i.FCRAFT_JOB_BOOKING_ID = hc.ViewModel.HandlingJobs.FCRAFT_JOB_BOOKING_ID
                i.OperateType = "partfinish"
            }

            const isPartFinish = hc.ViewModel.operationType === "finish";
            const isBatchSplit = hc.ViewModel.operationType === "split";
            const actionText = isPartFinish ? "部分完工" : "批量拆分完工";

            hc.Command.ConfirmDialog({
                title: "提示",
                content: `<div>
                            确定要${actionText}吗？
                            ${context}
                          </div>` ,
                confirm() {
                    if (isBatchSplit) {
                        hc.ViewModel.QueryBatchSplitAsync(); //批量拆分接口
                    } else {
                        hc.ViewModel.QueryFinishJobAsync(); //部分完工接口
                    }
                }
            });
        },
        // 完工
        QueryFinishJobAsync() {
            const row = hc.ViewModel.gridFinishiItem.props.rowData

            //按需获取标签数据
            if (row[0].FIF_TAG == true) {
                for (const i of row) {
                    i.FCHECK_PERSON_ID = hc.Command.GetUserInfo().UserPsnId;
                    if (hc.ViewModel.centerDialogItemVisible) {
                        i.tagItems = hc.Controls.gridFinishChild.getValidData();//获取明细表所有数据
                    }
                }
            }

            hc.ViewModel.centerDialogVisible = false //在这里直接关闭完工填写不良窗口
            hc.Command.LoadData(hc.ViewModel.api.FinishJobAsync, {
                models: row
            }, hc.ViewModel.SubmitFormSuccess);
        },
        // 完工后
        SubmitFormSuccess(res) {
            if (res.StatusCode == 200) {
                hc.ViewModel.HandlingJobs = res.Entity[0]
                hc.ViewModel.FWORK_STATUS = res.Entity[0].FWORK_STATUS
                hc.ViewModel.FACT_ST_DATE = res.Entity[0].FACT_ST_DATE
                hc.ViewModel.FACT_ED_DATE = res.Entity[0].FACT_ED_DATE
                hc.ViewModel.FACT_USE_HOUR = res.Entity[0].FACT_USE_HOUR

                hc.ViewModel.getWaitJobsData();

                // 部分完工成功后单独保存 不合格数量、不良原因
                //if (hc.ViewModel.gridFinishiItem.props.rowData[0].FNG_QTY) {//当不合格数量有值时才单独保存 不合格数量、不良原因
                const finishItems = hc.ViewModel.gridFinishiItem.props.rowData;
                let ngItems = [];
                if (hc.ViewModel.gridFinishiItem.props.rowData[0].FNG_QTY) {
                    ngItems = hc.Controls.ngItem.getChangeData();
                }
                hc.ViewModel.gridFinishChild.props.rowData = hc.ViewModel.gridFinishiItem.props.rowData[0].tagItems
                if (finishItems && finishItems.length > 0) {
                    const {
                        FCRAFT_JOB_BOOKING_ID,
                        FCRAFT_SCHEDULE_ID,
                        FSALE_ORDER_ID,
                        FWORK_ORDER_ID,
                        FMATERIAL_ID,
                        FCRAFT_ID,
                        FSTATION_ID,
                        FCHECK_QC_DATE,
                    } = finishItems[0];
                    const { UserPsnId } = hc.Command.GetUserInfo();

                    let FNG_QTY = 0;

                    for (const i of ngItems) {
                        FNG_QTY += i.FNG_QTY;
                        i.FCRAFT_JOB_BOOKING_BAD_ID = hc.Command.GetGuid();
                        i.FCRAFT_JOB_BOOKING_ID = FCRAFT_JOB_BOOKING_ID;
                        i.FCRAFT_SCHEDULE_ID = FCRAFT_SCHEDULE_ID;
                        // 为数组时取最后一个原因
                        if (i.FBAD_REASON_ID && hc.Command.IsArray(i.FBAD_REASON_ID)) {
                            i.FBAD_REASON_ID = i.FBAD_REASON_ID.pop()
                        }
                        i.AttachList = []
                    }

                    const saveData = {
                        FCHECK_QC_DATE,
                        FCRAFT_JOB_BOOKING_ID,
                        FCRAFT_SCHEDULE_ID,
                        FSALE_ORDER_ID,
                        FWORK_ORDER_ID,
                        FMATERIAL_ID,
                        FCRAFT_ID,
                        FSTATION_ID,
                        FNG_QTY,
                        FNG_WEIGHT: 0,
                        FCHECK_PERSON_ID: UserPsnId, // 检验人员
                        FCHK_TYPE: "full", // 检验类型 全检
                        BadList: ngItems
                    }
                    console.log(saveData)
                    hc.ViewModel.saveQcLoadData(saveData);
                } else {
                    hc.Command.LoadData(hc.ViewModel.api.chuangPaiSaveNg, {
                        model: hc.Controls.ngItem.getChangeData()
                    }, res => {
                        if (res.StatusCode != 200) hc.Command.ShowResMessage(res);
                    });
                }
                //}
            } else {
                hc.Command.ShowResMessage(res);
            }
        },
        // 批量拆分完工
        QueryBatchSplitAsync() {
            const row = hc.ViewModel.gridFinishiItem.props.rowData

            //按需获取标签数据
            if (row[0].FIF_TAG == true) {
                for (const i of row) {
                    i.FCHECK_PERSON_ID = hc.Command.GetUserInfo().UserPsnId;
                    if (hc.ViewModel.centerDialogItemVisible) {
                        i.tagItems = hc.Controls.gridFinishChild.getValidData();//获取明细表所有数据
                    }
                }
            }

            hc.ViewModel.centerDialogVisible = false //在这里直接关闭完工填写不良窗口
            hc.Command.LoadData(hc.ViewModel.api.BatchSplitAsync, {
                models: row
            }, hc.ViewModel.BatchSplitSuccess);
        },
        // 批量拆分完工后处理
        BatchSplitSuccess(res) {
            if (res.StatusCode == 200) {
                // 批量拆分返回的数据结构可能不同，需要适配
                const splitResults = res.Entity;
                if (splitResults && splitResults.length > 0) {
                    const firstResult = splitResults[0];
                    if (firstResult.OriginalSchedule) {
                        hc.ViewModel.HandlingJobs = firstResult.OriginalSchedule;
                        hc.ViewModel.FWORK_STATUS = firstResult.OriginalSchedule.FWORK_STATUS;
                        hc.ViewModel.FACT_ST_DATE = firstResult.OriginalSchedule.FACT_ST_DATE;
                        hc.ViewModel.FACT_ED_DATE = firstResult.OriginalSchedule.FACT_ED_DATE;
                        hc.ViewModel.FACT_USE_HOUR = firstResult.OriginalSchedule.FACT_USE_HOUR;
                    }
                }

                hc.ViewModel.getWaitJobsData();

                // 显示拆分结果信息
                let message = "批量拆分完工成功！\n";
                splitResults.forEach((result, index) => {
                    if (result.SplitSchedule) {
                        message += `任务${index + 1}: 已完成拆分，生成新批次号：${result.SplitSchedule.FLOT_NO}\n`;
                    }
                    if (result.DownstreamTasks && result.DownstreamTasks.length > 0) {
                        message += `    ┗━ 已创建${result.DownstreamTasks.length}个下游任务\n`;
                    }
                });

                hc.ViewModel.$message.success(message);

                // 处理不合格数量和不良原因（与普通完工相同）
                const finishItems = hc.ViewModel.gridFinishiItem.props.rowData;
                let ngItems = [];
                if (hc.ViewModel.gridFinishiItem.props.rowData[0].FNG_QTY) {
                    ngItems = hc.Controls.ngItem.getChangeData();
                }
                hc.ViewModel.gridFinishChild.props.rowData = hc.ViewModel.gridFinishiItem.props.rowData[0].tagItems

                if (finishItems && finishItems.length > 0) {
                    const {
                        FCRAFT_JOB_BOOKING_ID,
                        FCRAFT_SCHEDULE_ID,
                        FSALE_ORDER_ID,
                        FWORK_ORDER_ID,
                        FMATERIAL_ID,
                        FCRAFT_ID,
                        FSTATION_ID,
                        FCHECK_QC_DATE,
                    } = finishItems[0];
                    const { UserPsnId } = hc.Command.GetUserInfo();

                    let FNG_QTY = 0;

                    for (const i of ngItems) {
                        FNG_QTY += i.FNG_QTY;
                        i.FCRAFT_JOB_BOOKING_BAD_ID = hc.Command.GetGuid();
                        i.FCRAFT_JOB_BOOKING_ID = FCRAFT_JOB_BOOKING_ID;
                        i.FCRAFT_SCHEDULE_ID = FCRAFT_SCHEDULE_ID;
                        // 为数组时取最后一个原因
                        if (i.FBAD_REASON_ID && hc.Command.IsArray(i.FBAD_REASON_ID)) {
                            i.FBAD_REASON_ID = i.FBAD_REASON_ID.pop()
                        }
                        i.AttachList = []
                    }

                    const saveData = {
                        FCHECK_QC_DATE,
                        FCRAFT_JOB_BOOKING_ID,
                        FCRAFT_SCHEDULE_ID,
                        FSALE_ORDER_ID,
                        FWORK_ORDER_ID,
                        FMATERIAL_ID,
                        FCRAFT_ID,
                        FSTATION_ID,
                        FNG_QTY,
                        FNG_WEIGHT: 0,
                        FCHECK_PERSON_ID: UserPsnId, // 检验人员
                        FCHK_TYPE: "full", // 检验类型 全检
                        BadList: ngItems
                    }
                    console.log(saveData)
                    hc.ViewModel.saveQcLoadData(saveData);
                } else {
                    hc.Command.LoadData(hc.ViewModel.api.chuangPaiSaveNg, {
                        model: hc.Controls.ngItem.getChangeData()
                    }, res => {
                        if (res.StatusCode != 200) hc.Command.ShowResMessage(res);
                    });
                }
            } else {
                hc.Command.ShowResMessage(res);
            }
        },

        saveQcLoadData(data) {
            hc.Command.LoadData(hc.ViewModel.api.QcNgSave, {
                model: data
            }, res => {
                if (res.StatusCode != 200) hc.Command.ShowResMessage(res);
            });
        },


        //合并操作行
        mergeOperateRows(operateRows) {
            let data = hc.ViewModel.gridWaitJobs.props.rowData;
            let refreshRows = [];
            data.map(dataRow => {
                let findRow = operateRows.find(resRow => {
                    return resRow.FCRAFT_SCHEDULE_ID == dataRow.FCRAFT_SCHEDULE_ID;
                })
                if (findRow) {
                    Object.assign(dataRow, findRow);
                    refreshRows.push(dataRow);
                }
            })
            hc.Controls.gridWaitJobs.refreshRows({
                dataRows: refreshRows
            });
        },


        //合格数量改变
        FPASS_QTY_change() {
            if (hc.ViewModel.mst.FUNIT_WEIGHT) {
                hc.ViewModel.FPASS_WEIGHT = hc.Command.Round(hc.ViewModel.FPASS_QTY * hc.ViewModel.mst.FUNIT_WEIGHT, 2);
            }
        },
        //不合格数量改变
        FNG_QTY_change() {
            if (hc.ViewModel.mst.FUNIT_WEIGHT) {
                hc.ViewModel.FNG_QTY = hc.Command.Round(hc.ViewModel.FNG_QTY * hc.ViewModel.mst.FUNIT_WEIGHT, 2);
            }
        },

        //下载文件
        downFile(attachId) {
            hc.Command.LoadArrayBuffer(hc.Command.GetAttachmentURL(attachId), null, { el: hc.Controls.gridESOP });
        },

        //预览PDF弹窗
        showDateDia(params, event) {
            //获取当前视窗高度 设置预览pdf弹窗的高度
            let height = document.body.clientHeight
            hc.ViewModel.objectHeight = `${height - 60}px`

            if (params.FATTACHMENT_ID) {
                let api = hc.ViewModel.api.GetAttachmentInfoAsync
                let id = params.FATTACHMENT_ID
                hc.Command.LoadData(api, {
                    id
                }, (res) => {
                    // console.log(res)
                    if (res.StatusCode == 200 && res.Entity.FATTACHMENT_FILE_PATH) {
                        let pdfSrc = `http://192.168.88.251:5000/${res.Entity.FATTACHMENT_FILE_PATH}`
                        hc.ViewModel.pdfSrc = pdfSrc
                        hc.ViewModel.pdfDialogVisible = true
                    } else {
                        hc.Command.ExceptionDialog({
                            content: res.Message
                        });
                    }
                })
            }
        },


        //排程任务表内方法
        gridWaitJobsEventsHander(name, arg1, arg2) {
            // console.log(name, arg1, arg2)
            switch (name) {
                case "selectionChanged":
                    setTimeout(() => {
                        hc.Controls.print.resetLoadingReports();
                    }, 1000);
                    hc.ViewModel.gridWaitJobsSelect();
                    break;
                case "rowClicked":
                    setTimeout(() => {
                        hc.Controls.print.resetLoadingReports();
                    }, 1000);
                    hc.ViewModel.getSopPic(arg1);//获取ESOP图片详情
                    hc.ViewModel.getChkPatrolData(arg1);//获取巡检单详情
                    break;
            }
        },
        //点击行时方法
        gridWaitJobsSelect() {
            const selectData = hc.Controls.gridWaitJobs.api.getSelectedRows();
            if (selectData.length == 0) {
                return
            }
            //查询操作之后把把所选中行的字体颜色恢复默认 gridWaitJobs
            if (selectData[0].status == 'new') {
                // let rowData=JSON.parse(JSON.stringify(hc.ViewModel))
                selectData[0].status = ''

                const rowData = JSON.parse(JSON.stringify(hc.ViewModel.gridWaitJobs.props.rowData))

                hc.ViewModel.gridWaitJobs.props.rowData = rowData
            }

            hc.ViewModel.NumChange()
        },

        //报工明细表，取消完工
        cancelFinished() {
            const rows = hc.Controls.gridJobBooking.api.getSelectedRows();
            if (!rows.length) {
                return hc.Command.WarnDialog({
                    content: hc.ViewModel.$t("请选择单据")
                });
            }
            for (i = 0; i < rows.length; i++) {
                if (rows[i].FIS_OUT == 1) {
                    return hc.Command.WarnDialog({
                        content: hc.ViewModel.$t("排程任务单编号" + rows[i].FCRAFT_SCHEDULE_NO + "为委外单据不可手动取消完工")
                    });
                }
            }
            hc.Command.ShowConfirm(hc.ViewModel.$t("您确定要取消完工选中的单据吗?"), null, () => {
                const ids = rows.map(item => item.FCRAFT_JOB_BOOKING_ID)
                hc.Command.LoadData(hc.ViewModel.api.cancelFinished, {
                    ids: ids
                }, res => {
                    if (res.StatusCode == 200) {
                        let rowdata = hc.ViewModel.gridJobBooking.props.rowData;
                        hc.ViewModel.delRowData(rowdata, ids)
                    } else {
                        hc.Command.ShowResMessage(res);
                    }
                }, {
                    el: hc.Controls.gridJobBooking
                });
            });
        },
        delRowData(rowdata, ids) {
            _.remove(rowdata, item => {
                for (let i of ids) {
                    if (item.FCRAFT_JOB_BOOKING_ID == i) {
                        return true
                    }
                }
            });
            hc.ViewModel.gridJobBooking.props.rowData = rowdata.slice()
        },

        //排程表不良明细点击事件
        openNgItem(record) {
            console.log(record);
            hc.Command.LoadData(hc.ViewModel.api.GetBadByIdAsync,
                {
                    id: record.FCRAFT_SCHEDULE_ID
                },
                res => {
                    if (res.StatusCode == 200) {
                        if (res.Entity != null) {
                            hc.ViewModel.controls.props.itemDialog.visible = true;

                            let num = 1;
                            res.Entity.forEach(item => {
                                if (item.AttachList && item.AttachList.length > 0) {
                                    for (const j of item.AttachList) {
                                        item[`FATTACH${num}_ID`] = j.FATTACH_ID;
                                        num++;
                                    }
                                }
                            })
                            hc.ViewModel.badgridItem.props.rowData = res.Entity
                        }
                    } else {
                        hc.Command.ShowResMessage(res)
                    }
                })
        },

        cancelItemDialog() {
            hc.ViewModel.controls.props.itemDialog.visible = false;
        },

        //报工明细表，保存修改
        UpdateJobDialogSave() {
            if (hc.ViewModel.UpdateJobData.FPASS_QTY === '') {
                return hc.Command.WarnDialog({
                    content: '请输入合格数量'
                });
            }

            if (hc.ViewModel.UpdateJobData.FNG_QTY === '') {
                return hc.Command.WarnDialog({
                    content: '请输入不良数量'
                });
            }

            hc.ViewModel.$confirm('确定要保存吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                hc.ViewModel.saveUpdate(); //请求接口数据
            })
        },
        saveUpdate() {
            hc.Command.LoadData(hc.ViewModel.api.saveJob, {
                model: hc.ViewModel.UpdateJobData
            }, res => {
                if (res.StatusCode == 200) {

                    //当成功时，判断是否需要作废
                    if (!hc.ViewModel.cancel_FJUAN_STATUS) {
                        if (hc.ViewModel.FJUAN_STATUS) {
                            hc.ViewModel.canceljobbooking()
                        }
                    }
                    //---end----

                    hc.ViewModel.getJobBookingList()
                    hc.ViewModel.UpdateJobDialogVisible = false;
                } else {
                    hc.Command.ShowResMessage(res)
                }
            })
        },

        //作废报工明细行
        canceljobbooking() {
            let id = hc.ViewModel.UpdateJobData.FCRAFT_JOB_BOOKING_ID
            let api = hc.ViewModel.api.canceljobbooking
            hc.Command.LoadData(api, { id }, (res) => {
                if (res.StatusCode == 200) {
                    // console.log('作废成功')
                    // hc.Command.ShowMessage('补打成功')
                } else {
                    hc.Command.ShowResMessage(res)
                }
            })
        },

        // 补打标签
        reimburselable(id) {

            hc.ViewModel.$confirm('确定要补打标签吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let api = hc.ViewModel.api.reimburselable
                hc.Command.LoadData(api, { id }, (res) => {
                    if (res.StatusCode == 200) {
                        hc.Command.ShowMessage('补打成功')
                    } else {
                        hc.Command.ShowResMessage(res)
                    }
                })
            })
        },

        //报废状态该表时
        FJUAN_STATUS_change() {
            // console.log('报废', hc.ViewModel.FJUAN_STATUS)

            if (!hc.ViewModel.FJUAN_STATUS) {// false 作废 去掉打勾

                hc.ViewModel.UpdateJobData.FNG_QTY = 0 //不良数量 改为 0
                hc.ViewModel.UpdateJobData.FPASS_QTY = hc.ViewModel.UpdateJobData_FPASS_QTY_bak //恢复为原来的合格数量
            }

            if (hc.ViewModel.FJUAN_STATUS) {// true 作废 打勾

                hc.ViewModel.UpdateJobData_FPASS_QTY_bak = hc.ViewModel.UpdateJobData.FPASS_QTY //先备份原来的 合格数量
                hc.ViewModel.UpdateJobData.FPASS_QTY = 0 // 合格数量 赋值为 0
                hc.ViewModel.UpdateJobData.FNG_QTY = hc.ViewModel.UpdateJobData_FPASS_QTY_bak//不良数量 赋值 为原来合格数量
            }
        },

        /**
         * 排序排程任务列表
         * 先按工单号降序排列，工单号相同时按排序号降序排列
         * 最后过滤掉已完成的任务
         * @param {Array} data - 排程任务数据数组
         * @returns {Array} - 排序并过滤后的数据
         */
        sortGridWaitJobs(data) {
            // 对数据进行排序
            data.sort((a, b) => {
                // 首先按工单号降序排序（工单号大的在前）
                const workOrderCompare = b.FWORK_ORDER_NO.localeCompare(a.FWORK_ORDER_NO);
                if (workOrderCompare !== 0) {
                    return workOrderCompare;
                }

                // 工单号相同时，按排序号降序排序
                return b.FSHOW_SEQNO - a.FSHOW_SEQNO;
            });

            // 过滤掉已完成的任务
            //return data.filter(item => item.FCRAFT_STATUS !== "finished");
            return data;//佳乐已结案的需要查看，去除筛选
        },

        //级联选择工位改变方法
        FSTATION_ID_Change(a, b) {
            hc.ViewModel.getStationData()
        },


        //切换用户按钮
        switchUser() {
            hc.ViewModel.SwitchUserDialogVisible = true
            // hc.Command.OpenReLoginPageDialog();
            // hc.ViewModel.LoginValidate();
        },

        //打开选择用户列表
        OpenSwitchUserList() {
            hc.ViewModel.SwitchUserListVisible = true
            // hc.ViewModel.getUserList()//获取用户列表方法
        },
        //选择用户时查询
        FDEPT_ID_change(e) {
            console.log(e)
            if (e) {
                hc.ViewModel.getUserList()
            } else {
                hc.ViewModel.AllEmpData = []
            }
        },
        //获取选择用户列表方法
        getUserList() {
            let api = hc.ViewModel.api.GetAllEmp
            let model = {
                "PageSize": 9999,
                "PageIndex": 1,
                // "RecordTotal": 32,
                "WhereGroup": {
                    "Items": [{
                        "FieldName": "FMAKER_ID",
                        "Value": hc.ViewModel.FDEPT_ID,//选择的部门id
                        "OperatorType": "Equal"
                    }],
                    "GroupType": "AND", "Groups": []
                }
            }
            hc.Command.LoadData(api, { model }, (res) => {
                if (res.StatusCode == 200) {
                    hc.ViewModel.AllEmpData = res.Entity
                }
            }, { disableLoading: false, el: hc.Controls.SwitchUserList })
        },
        //选择用户行方法
        handleCurrentChange(selectData, selectOldData) {
            // console.log(a, b, c)
            hc.ViewModel.selectEmpData = selectData
        },
        //确定选中的用户
        sureSwitchUser() {
            if (hc.ViewModel.selectEmpData) {
                hc.ViewModel.SwitchUserFormMst.FUSERNAME = hc.ViewModel.selectEmpData.FMOBILE
                hc.ViewModel.SwitchUserFormMst.FPASSWORD = ''
            }
            hc.ViewModel.SwitchUserListVisible = false
        },
        //用户登录校验
        LoginForm() {
            hc.Controls.SwitchUserForm.validate((valid, a) => {
                if (valid) {
                    hc.ViewModel.LoginValidate();
                }
            });
        },
        LoginValidate() {
            const ac = hc.ViewModel.SwitchUserFormMst.FUSERNAME;
            const ap = hc.ViewModel.SwitchUserFormMst.FPASSWORD;
            const nc = hc.ViewModel.SwitchUserFormMst.IsNeedCode;
            const ck = hc.ViewModel.SwitchUserFormMst.CodeKey;
            const lg = hc.ViewModel.SwitchUserFormMst.FLANG;

            const encrypt = new hc.Plugin["jsencrypt"].JSEncrypt();//new JSEncrypt();
            encrypt.setPublicKey(hc.Data.PublicKey);
            const params = { FUSERNAME: ac, FPASSWORD: ap, IsNeedCode: nc, CodeKey: ck, FLANG: lg };//$.extend({}, ac, ap);

            const encrypted = encrypt.encrypt(hc.Command.Serialize(params));
            encrypted = encrypted.replace(/\+/g, '-').replace(/\//g, '_');
            while (encrypted[encrypted.length - 1] == '=') { encrypted = encrypted.slice(0, encrypted.length - 1); }

            //by ggw 20200411 增加传入版本
            const version = hc.Command.GetMenuRootVersion();
            let version_str = '';
            if (version != null) {
                version_str = hc.Command.Serialize(version);
            }
            //取有效的url
            hc.Command.Request({
                url: `${hc.Config.websiteURL}/Login?handler=Login`,
                type: 'GET',
                dataType: "json",
                contentType: 'application/json',
                header: {
                    secret: encrypted,
                    rightver: version_str,
                },
                success(res) {
                    if (res.StatusCode != 200 && res.StatusCode != 1100 && res.StatusCode != 1200) {
                        hc.Command.ShowMessage(`登录失败！${res.Message}`);
                    }
                    else {
                        hc.Command.SetRunStatus(1);
                        hc.Command.ShowMessage(hc.ViewModel.$t('登录成功'));
                        hc.Command.SetUUID(res.Entity.Guid);
                        hc.Command.SetUserInfo(JSON.stringify(res.Entity));
                        hc.Command.RefreshUserInfo();

                        //重新赋值新的登录人员
                        const userInfo = hc.Command.GetUserInfo();
                        hc.UserInfo = userInfo
                        //重新获取用户信息
                        hc.ViewModel.getUserInfo()
                        hc.ViewModel.SwitchUserDialogVisible = false
                    }
                }
            });
        },


        //预览最新的ESOP按钮，已经按日期排序，第一个即最新
        showLastSop() {
            if (hc.ViewModel.gridESOP.props.rowData.length > 0) {
                hc.ViewModel.showDateDia(hc.ViewModel.gridESOP.props.rowData[0])
            }
        },

        //不合格数量弹窗列表新增行函数
        ngItemEventHandler(name, arg1) {
            switch (name) {
                case "newAddRow":
                    hc.ViewModel.gridAddRow(arg1);
                    break;
            }
        },
        gridAddRow(callback) {
            let rowData = hc.ViewModel.gridFinishiItem.props.rowData[0]
            let model = {
                //加工任务id
                FCRAFT_JOB_BOOKING_ID: rowData.HandlingJobs[0].FCRAFT_JOB_BOOKING_ID,
                //排程任务Id
                FCRAFT_SCHEDULE_ID: rowData.FCRAFT_SCHEDULE_ID,
                //不合格数量
                FNG_QTY: null,
            };
            callback(model);
        },

        //不合格数量弹窗确定函数
        ngDialogSure() {
            // console.log(hc.Controls.ngItem.getChangeData())
            let rowData = hc.Controls.ngItem.getChangeData()
            let FNG_QTY_total = 0
            for (let i of rowData) {
                if (!i.FNG_QTY || !i.FBAD_REASON_ID) {
                    hc.Command.WarnDialog({
                        content: '请填写完整数据'
                    })
                    return false
                }
                //汇总总的不合格数量
                FNG_QTY_total = hc.ViewModel.accAdd(FNG_QTY_total, i.FNG_QTY)
            }
            // 将不合格数量 赋值到部分完工弹窗的 不合格数量中
            hc.ViewModel.gridFinishiItem.props.rowData[0].FNG_QTY = FNG_QTY_total
            // 刷新列
            hc.Controls.gridFinishiItem.refreshCols({ fields: ["FNG_QTY"] });
            hc.ViewModel.ngDialogVisible = false
        },

        //加法函数
        accAdd(arg1, arg2) {
            var r1, r2, m;
            try {
                r1 = arg1.toString().split(".")[1].length;
            } catch (e) {
                r1 = 0;
            }
            try {
                r2 = arg2.toString().split(".")[1].length;
            } catch (e) {
                r2 = 0;
            }
            m = Math.pow(10, Math.max(r1, r2));
            return (arg1 * m + arg2 * m) / m;
        },


        /**
         * 格式化不良原因树形结构
         * @param {Array} reasons - 不良原因列表
         * @param {Array} categories - 不良原因分类列表
         * @returns {Array} - 格式化后的树形结构
         */
        formatBadReasonTree(reasons, categories) {
            // 格式化分类数据
            const formatCategories = (categories) => {
                categories.forEach(category => {
                    category.ID = category.FBAD_REASON_CATE_ID;
                    category.NAME = category.FBAD_REASON_CATE_NAME;
                    if (category.SubCates) {
                        formatCategories(category.SubCates);
                    }
                });
            };

            // 将原因数据插入到对应分类中
            const insertReasonsToCategory = (reason, categories) => {
                categories.forEach(category => {
                    if (reason.FBAD_REASON_CATE_ID === category.FBAD_REASON_CATE_ID) {
                        if (!category.SubCates) {
                            category.SubCates = [];
                        }
                        category.SubCates.push(reason);
                    } else if (category.SubCates) {
                        insertReasonsToCategory(reason, category.SubCates);
                    }
                });
            };

            try {
                // 格式化所有分类
                formatCategories(categories);

                // 格式化并插入所有原因
                reasons.forEach(reason => {
                    reason.ID = reason.FBAD_REASON_ID;
                    reason.NAME = reason.FBAD_REASON_NAME;
                    insertReasonsToCategory(reason, categories);
                });

                return categories;
            } catch (error) {
                console.error('格式化不良原因树形结构失败:', error);
                return categories;
            }
        },

        //当排程任务刷新时，获取额外的排程任务信息 函数
        getWaitJobsExtra() {
            if (hc.ViewModel.gridWaitJobs.props.rowData.length === 0) return false
            let ids = []
            for (let i of hc.ViewModel.gridWaitJobs.props.rowData) {
                ids.push(i.FCRAFT_SCHEDULE_ID)
            }
            hc.Command.LoadData(hc.ViewModel.api.GetCraftSchByIds, { ids }, (res) => {
                if (res.StatusCode == 200) {
                    // console.log(res)
                    for (let i of hc.ViewModel.gridWaitJobs.props.rowData) {
                        for (let j of res.Entity) {
                            if (i.FCRAFT_SCHEDULE_ID === j.FCRAFT_SCHEDULE_ID) {
                                i.FLOT_NO = j.FLOT_NO
                                break
                            }
                        }
                    }
                    hc.Controls.gridWaitJobs.refreshCols({ fields: ["FLOT_NO"] });
                } else {
                    hc.Command.ExceptionDialog({
                        content: res.Message
                    });
                }
            }, { disableLoading: true })
        },

        //当排程任务刷新时，根据工单获取前工艺总不良数
        getWaitJobsAllFail() {
            if (hc.ViewModel.gridWaitJobs.props.rowData.length === 0) return false
            const a = []
            for (let i of hc.ViewModel.gridWaitJobs.props.rowData) {
                a.push(i.FWORK_ORDER_ID)
            }
            const ids = [...new Set(a)];//过滤重复的id
            hc.Command.LoadData(hc.ViewModel.api.GetWorkOrdJobBookingBad, { ids }, (res) => {
                if (res.StatusCode == 200) {
                    // console.log(res)
                    for (let i of hc.ViewModel.gridWaitJobs.props.rowData) {
                        for (let j of res.Entity) {
                            if (i.FWORK_ORDER_ID === j.FWORK_ORDER_ID) {
                                i.BadQty = j.BadQty
                                break
                            }
                        }
                    }
                    hc.Controls.gridWaitJobs.refreshCols({ fields: ["BadQty"] });
                } else {
                    hc.Command.ExceptionDialog({
                        content: res.Message
                    });
                }
            }, { disableLoading: true })
        },

        //工位选择栏顺序按照 升序排列
        // sortSubCates(data) {
        //     // 对数组进行排序  
        //     data.sort((a, b) => {
        //         const nameA = a.NAME
        //         const nameB = b.NAME
        //         if (nameA < nameB) {
        //             return -1;
        //         }
        //         if (nameA > nameB) {
        //             return 1;
        //         }
        //         // name 必须相等
        //         return 0;
        //     });

        //     //遍历，如果含有SubCates 则继续递归调用
        //     for (let i of data) {
        //         if (i.SubCates && i.SubCates.length > 0) {
        //             i.SubCates = hc.ViewModel.sortSubCates(i.SubCates)
        //         }
        //     }

        //     return data;
        // },

        sortSubCates(data) {
            // 优化后的排序逻辑：支持数字自然排序（如 "2" < "10"）
            data.sort((a, b) => {
                const nameA = a.NAME.trim();
                const nameB = b.NAME.trim();

                // 使用自然排序规则：数字按数值大小比较
                return nameA.localeCompare(nameB, 'zh-CN', {
                    numeric: true,    // 启用数字智能识别
                    sensitivity: 'base', // 忽略大小写和重音差异
                    ignorePunctuation: true // 忽略标点符号影响
                });
            });

            // 递归处理子节点
            for (let item of data) {
                if (item.SubCates?.length > 0) {
                    item.SubCates = this.sortSubCates(item.SubCates);
                }
            }
            return data;
        },


        // 获取设备信息
        getDevData() {
            if (hc.ViewModel.mst.FBOOK_ID) {
                hc.Command.LoadData(hc.ViewModel.api.getDevData,
                    {
                        id: hc.ViewModel.mst.FBOOK_ID
                    },
                    res => {
                        if (res.StatusCode == 200) {
                            hc.ViewModel.machine = res.Entity?.FGATEWAY_STR//网关记录下来
                        } else {
                            hc.Command.ShowResMessage(res)
                        }
                    }
                )
            }
        },

        getReportCraftId(callBack) {

            console.log(hc.ViewModel.gridFinishiItem.props.rowData)
            if (hc.ViewModel.centerDialogVisible) {
                return hc.ViewModel.gridFinishiItem.props.rowData[0]?.FCRAFT_ID || null
            }

            if (!hc.ViewModel.centerDialogVisible) {
                return hc.ViewModel.gridWaitJobs.props.rowData[0]?.FCRAFT_ID || null
            }

            return null
        },

        GeneratePackCode() {
            hc.Command.LoadData(hc.ViewModel.api.CreatePackBarCodeAsync, {
                FCRAFT_SCHEDULE_ID: hc.ViewModel.mst.FCRAFT_SCHEDULE_ID
            }, res => hc.ViewModel.codeEnterSuccess(res))

        }
    }
})
