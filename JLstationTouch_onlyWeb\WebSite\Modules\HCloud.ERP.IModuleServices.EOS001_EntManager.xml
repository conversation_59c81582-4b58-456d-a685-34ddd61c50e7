<?xml version="1.0"?>
<doc>
    <assembly>
        <name>HCloud.ERP.IModuleServices.EOS001_EntManager</name>
    </assembly>
    <members>
        <member name="T:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientEntInitService">
            <summary>
            企业端企业初始化接口
            </summary>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientEntInitService.AddInitLogAsync(HCloud.ERP.IModuleServices.CPF001_EntManager.Models.EntInitLogModel)">
            <summary>
            写入log
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientEntInitService.GetStatusAsync">
            <summary>
            获取状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientEntInitService.GetEntInitConfigAsync(HCloud.Core.ProxyGenerator.Models.QueryRequestModel)">
            <summary>
            获取企业初始化配置
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientEntInitService.SaveEntInitConfigAsync(System.Collections.Generic.List{HCloud.ERP.IModuleServices.CPF001_EntManager.Models.EntInitConfigModel})">
            <summary>
            企业初始化配置保存
            </summary>
            <param name="list"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientEntInitService.DeleteEntInitConfigAsync(System.Collections.Generic.List{System.String})">
            <summary>
            企业初始化配置批量删除
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="T:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientEntService">
            <summary>
            企业端 企业管理接口
            </summary>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientEntService.VerifyMobileAsync(HCloud.ERP.IModuleServices.CPF001_EntManager.Models.EntModel)">
            <summary>
            验证手机
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientEntService.RegAsync(HCloud.ERP.IModuleServices.CPF001_EntManager.Models.EntModel)">
            <summary>
            注册
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="T:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService">
            <summary>
            用户凭据接口
            </summary>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.GetPublicLoginKeyAsync">
            <summary>
            RSA 公钥
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.LoginAsync(System.String)">
            <summary>
            登录接口 
            </summary>
            <param name="secret">RSA 加密字符串</param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.RequestFindPasswordAsync(HCloud.ERP.IModuleServices.CPF001_EntManager.Models.FindPasswordModel)">
            <summary>
            找回密码 验证手机
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.FindPasswordAsync(HCloud.ERP.IModuleServices.CPF001_EntManager.Models.FindPasswordModel)">
            <summary>
            找回密码 验证验证码
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.UpdatePasswordAsync(HCloud.ERP.IModuleServices.CPF001_EntManager.Models.UpdateModel)">
            <summary>
            修改密码
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.ChangeEntAsync(System.String)">
            <summary>
            切换企业
            </summary>
            <param name="ecode"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.InitEntSysAsync">
            <summary>
            初始化企业子系统
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.InitEntSuccessAsync">
            <summary>
            初始化企业成功 
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.CheckUserContextAsync">
            <summary>
            验证用户上下文是否已失效
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.GetEntByUserAsync">
            <summary>
            获取企业  不需要传参数，直接根据登录信息获取
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.LogoutAsync">
            <summary>
            退出登录接口
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.UpdateUserTokenEmpInfoAsync">
            <summary>
            更新缓存用户UserToken的员工信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.GetUserTokenAsync">
            <summary>
            返回UserToken对象
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.ValidateTokenAsync">
            <summary>
            验证token是否有效
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.ValidateTokenByModelAsync(HCloud.ERP.IModuleServices.EOS001_EntManager.Models.ValidateTokenModel)">
            <summary>
            验证token是否有效
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.BILoginAsync(System.String)">
            <summary>
            BI报表登录
            </summary>
            <param name="secret"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.GetQRCodeLoginImageAsync">
            <summary>
            二维码登录图片
            </summary>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.QRCodeLoginAsync(HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginRequestModel)">
            <summary>
            二维码登录
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.QRCodeScanAsync(HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginRequestModel)">
            <summary>
            二维码扫描
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.QRCodeConfirmLoginAsync(HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginRequestModel)">
            <summary>
            二维码登录确认
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.QRCodeCancelLoginAsync(HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginRequestModel)">
            <summary>
            二维码取消登录
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:HCloud.ERP.IModuleServices.EOS001_EntManager.IClientUserService.GetEntInitStatusAsync">
            <summary>
            取当前企业的初始化状态
            </summary>
            <returns></returns>
        </member>
        <member name="T:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginCacheModel">
            <summary>
            二维码登录缓存
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginCacheModel.IfScan">
            <summary>
            是否扫描成功
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginCacheModel.UserIP">
            <summary>
            确认登录用户IP
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginCacheModel.UserToken">
            <summary>
            确认登录用户token
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginCacheModel.IfConfirmLogin">
            <summary>
            用户是否确认登录
            </summary>
        </member>
        <member name="T:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginImageModel">
            <summary>
            二维码登录图片返回
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginImageModel.ImageBase64">
            <summary>
            图片base64字符串
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginImageModel.ID">
            <summary>
            唯一键
            </summary>
        </member>
        <member name="T:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginRequestModel">
            <summary>
            二维码请求
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.QRCodeLoginRequestModel.ID">
            <summary>
            唯一键
            </summary>
        </member>
        <member name="T:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.BILoginModel">
            <summary>
            BI登录
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.BILoginModel.Token">
            <summary>
            打印用户token
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.BILoginModel.IP">
            <summary>
            打印用户ip
            </summary>
        </member>
        <member name="T:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.ValidateTokenModel">
            <summary>
            验证token的model
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.ValidateTokenModel.Token">
            <summary>
            验证token
            </summary>
        </member>
        <member name="P:HCloud.ERP.IModuleServices.EOS001_EntManager.Models.ValidateTokenModel.IP">
            <summary>
            验证ip
            </summary>
        </member>
    </members>
</doc>
